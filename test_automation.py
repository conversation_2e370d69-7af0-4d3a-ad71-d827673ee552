#!/usr/bin/env python3
"""
🧪 TEST AUTOMATION SCRIPT
Verifies that the automation can work
"""

import asyncio
from ultimate_x_automation import get_daily_product, create_marketing_post

def test_product_generation():
    """Test product selection"""
    print("🧪 Testing product generation...")
    product = get_daily_product()
    print(f"✅ Product: {product['title']}")
    print(f"   Price: {product['price']}")
    print(f"   Commission: ${product['commission']}")
    return product

def test_post_creation(product):
    """Test post content creation"""
    print("\n🧪 Testing post creation...")
    post = create_marketing_post(product)
    print(f"✅ Post created ({len(post)} chars):")
    print("-"*50)
    print(post)
    print("-"*50)
    return post

async def test_browser():
    """Test browser launch"""
    print("\n🧪 Testing browser launch...")
    try:
        from playwright.async_api import async_playwright
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            await page.goto('https://x.com')
            title = await page.title()
            await browser.close()
            print(f"✅ Browser works! Loaded: {title}")
            return True
    except Exception as e:
        print(f"❌ Browser error: {e}")
        return False

def main():
    print("="*70)
    print("          🧪 AUTOMATION SYSTEM TEST")
    print("="*70)
    
    # Test components
    product = test_product_generation()
    post = test_post_creation(product)
    
    # Test browser
    browser_ok = asyncio.run(test_browser())
    
    print("\n" + "="*70)
    print("                 TEST RESULTS")
    print("="*70)
    
    print("\n✅ Product Generation: WORKING")
    print("✅ Post Creation: WORKING")
    if browser_ok:
        print("✅ Browser Automation: WORKING")
    else:
        print("❌ Browser Automation: NEEDS FIX")
    
    print("\n📊 Your affiliate URL:")
    print(f"   https://www.amazon.com/dp/{product['asin']}?tag=igorganapolsk-20")
    
    print("\n🎯 Ready for automation!")
    print("   Run: ./setup_automation.sh")
    
    return browser_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
