{"name": "💰 BULLETPROOF REAL MONEY AFFILIATE", "nodes": [{"parameters": {"triggerTimes": [{"mode": "everyDay", "hour": 9}]}, "name": "Daily Trigger", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [200, 300]}, {"parameters": {"functionCode": "// REAL HIGH-CONVERTING AFFILIATE PRODUCTS - HANDPICKED FOR MAXIMUM PROFIT\nconst profitableProducts = {\n  Monday: {\n    asin: 'B0CX23V2ZK',\n    title: 'Apple AirPods Pro (2nd Generation) - Active Noise Cancellation',\n    price: '$199.99',\n    category: 'Electronics',\n    rating: '4.4',\n    commission: '~$8.00'\n  },\n  Tuesday: {\n    asin: 'B08N5WRWNW',\n    title: 'Echo Dot (5th Gen, 2022) with Clock | Smart Speaker',\n    price: '$59.99',\n    category: 'Electronics', \n    rating: '4.7',\n    commission: '~$2.40'\n  },\n  Wednesday: {\n    asin: 'B085RGGJ9D',\n    title: 'Logitech MX Master 3 Advanced Wireless Mouse',\n    price: '$99.99',\n    category: 'Office',\n    rating: '4.5', \n    commission: '~$4.00'\n  },\n  Thursday: {\n    asin: 'B0BFZH6RGP',\n    title: 'Instant Pot Duo 7-in-1 Electric Pressure Cooker',\n    price: '$79.99',\n    category: 'Home & Kitchen',\n    rating: '4.6',\n    commission: '~$3.20'\n  },\n  Friday: {\n    asin: 'B08HWPZJ4N',\n    title: 'ASUS ROG Strix Gaming Mechanical Keyboard',\n    price: '$129.99',\n    category: 'Electronics',\n    rating: '4.3',\n    commission: '~$5.20'\n  },\n  Saturday: {\n    asin: 'B0863TXGM3',\n    title: 'Anker PowerCore 20000mAh Portable Charger', \n    price: '$45.99',\n    category: 'Electronics',\n    rating: '4.5',\n    commission: '~$1.84'\n  },\n  Sunday: {\n    asin: 'B08FC61F4Y',\n    title: 'Sony WH-CH720N Noise Canceling Wireless Headphones',\n    price: '$149.99',\n    category: 'Electronics', \n    rating: '4.4',\n    commission: '~$6.00'\n  }\n};\n\nconst days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\nconst today = days[new Date().getDay()];\nconst product = profitableProducts[today];\n\nreturn [{\n  json: {\n    asin: product.asin,\n    title: product.title,\n    price: product.price,\n    url: `https://www.amazon.com/dp/${product.asin}?tag=yourtag-20`,\n    image: `https://m.media-amazon.com/images/I/61${product.asin.slice(-6)}_AC_SX679_.jpg`,\n    rating: product.rating,\n    category: product.category,\n    commission: product.commission,\n    day: today\n  }\n}];"}, "name": "Select PROFITABLE Product", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [440, 300]}, {"parameters": {"url": "https://api.anthropic.com/v1/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"model\": \"claude-3-5-sonnet-20240620\",\n  \"max_tokens\": 250,\n  \"system\": \"You are a top-converting Amazon affiliate marketer. Write posts that turn browsers into buyers. Focus on real value and urgency that converts.\",\n  \"messages\": [\n    {\n      \"role\": \"user\", \n      \"content\": \"Write a compelling <=280 char X post for this Amazon product. Focus on REAL benefits for developers/remote workers. Include exact price, create urgency, and end with #ad. Be authentic and persuasive.\\n\\nTitle: {{ $json.title }}\\nPrice: {{ $json.price }}\\nRating: {{ $json.rating }} stars\\nCommission: {{ $json.commission }}\\n\\nMake it CONVERT into sales!\"\n    }\n  ]\n}", "options": {}}, "name": "Claude: CONVERTING Copy", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [700, 300], "credentials": {"httpHeaderAuth": {"id": "claude_api_key", "name": "<PERSON>"}}}, {"parameters": {"functionCode": "// Extract <PERSON>'s response and format for posting\nconst claudeResponse = $json.content && $json.content[0] && $json.content[0].text;\nconst generatedPost = claudeResponse || \n  `🔥 ${$('Select PROFITABLE Product').item.json.title} at ${$('Select PROFITABLE Product').item.json.price}! Perfect for remote work productivity. ${$('Select PROFITABLE Product').item.json.rating}⭐ rating. Limited time deal! #ad`;\n\nreturn [{\n  json: {\n    asin: $('Select PROFITABLE Product').item.json.asin,\n    title: $('Select PROFITABLE Product').item.json.title,\n    price: $('Select PROFITABLE Product').item.json.price,\n    url: $('Select PROFITABLE Product').item.json.url,\n    image: $('Select PROFITABLE Product').item.json.image,\n    content: generatedPost.substring(0, 270) + ' #ad',\n    rating: $('Select PROFITABLE Product').item.json.rating,\n    commission: $('Select PROFITABLE Product').item.json.commission\n  }\n}];"}, "name": "Format Post Content", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [940, 300]}, {"parameters": {"operation": "shorten", "longUrl": "={{$json.url}}"}, "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.bitly", "typeVersion": 1, "position": [1180, 300], "credentials": {"bitlyApi": "****************************************"}}, {"parameters": {"text": "={{$json.content.replace($json.url, $json.link)}}", "additionalFields": {"media": [{"mediaUrl": "={{$json.image}}"}]}}, "name": "Post to X", "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [1420, 300], "credentials": {"twitterOAuth2Api": "4nyJwIueb1AiO6M6bAq4fRWqG"}}, {"parameters": {"operation": "append", "sheetId": "1aYCw8uspcH76KfxRwITFs-mz26Z9ouQ9lhuZd8uG144/edit?pli=1&gid=0#gid=0", "range": "Products!A:K", "options": {"valueInputMode": "RAW", "values": {"values": ["={{$now}}", "={{$json.asin}}", "={{$json.title}}", "={{$json.price}}", "={{$json.commission}}", "={{$json.link}}", "={{$json.image}}", "={{$json.content}}", "TRUE", "0", "0"]}}}, "name": "Log PROFITS in Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 3, "position": [1660, 300], "credentials": {"googleApi": "NjK0bszo3le4otnv"}}], "connections": {"Daily Trigger": {"main": [[{"node": "Select PROFITABLE Product", "type": "main", "index": 0}]]}, "Select PROFITABLE Product": {"main": [[{"node": "Claude: CONVERTING Copy", "type": "main", "index": 0}]]}, "Claude: CONVERTING Copy": {"main": [[{"node": "Format Post Content", "type": "main", "index": 0}]]}, "Format Post Content": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Bitly Shorten": {"main": [[{"node": "Post to X", "type": "main", "index": 0}]]}, "Post to X": {"main": [[{"node": "Log PROFITS in Sheets", "type": "main", "index": 0}]]}}, "settings": {}}