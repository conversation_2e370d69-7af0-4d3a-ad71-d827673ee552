# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.env
.venv
.env.*
!.env.example

# IDE
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Project specific
*.log
logs/
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/

# n8n
n8n-startup-error.log
n8n-startup.log

# Local development
local_settings.py
local_settings/

# Credentials
*.pem
*.key
*.crt
*.p12
*.pfx
*.cer
*.p7b
*.p7c
*.p7s
*.p8
*.p12
*.pfx
*.pem
*.crt
*.key
*.csr
*.der
*.p7b
*.p7c
*.p7s
*.p8
*.p12
*.pfx
*.pem
*.crt
*.key
*.csr
*.der

# Project specific
config/*.json
!config/*.example.json

# Jupyter Notebook
.ipynb_checkpoints

# Local development
.local-*

# Docker
.docker/

# Temporary files
*.swp
*.swo
*~

# Logs and databases
*.log
*.sql
*.sqlite

# Environment files
.env
!.env.example
