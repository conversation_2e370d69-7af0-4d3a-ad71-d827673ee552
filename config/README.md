# n8n Self-Hosted Setup

A complete self-hosted n8n solution with 24/7 availability, self-healing, and secure remote access via Tailscale.

## 🎯 Features

- **24/7 Operation**: Auto-starts on system boot
- **Self-Healing**: Docker restart policies and health checks
- **Persistent Storage**: PostgreSQL database with Docker volumes
- **Secure Remote Access**: Tailscale VPN for mobile access
- **Authentication**: Basic auth protection
- **Production Ready**: Proper logging and monitoring setup

## 📁 Project Structure

```
~/n8n-self-hosted/
├── docker-compose.yml          # Main Docker Compose configuration
├── complete-setup.sh          # Final setup script (run after Tailscale)
├── manage.sh                  # Service management script
├── tailscale-setup.md         # Tailscale setup instructions
├── com.n8n.docker.plist       # macOS auto-start service
└── README.md                  # This file
```

## 🚀 Quick Start

### 1. Current Status
✅ Docker containers are running and healthy
✅ n8n is accessible at: http://localhost:5678
✅ PostgreSQL database configured with persistent storage
✅ Self-healing enabled with health checks and restart policies

### 2. Complete Tailscale Setup
Follow the instructions in `tailscale-setup.md`:

1. Open Tailscale app (Cmd+Space → "Tailscale")
2. Sign in to your Tailscale account
3. Authorize this Mac device
4. Install Tailscale on your mobile device
5. Run: `./complete-setup.sh`

### 3. Access n8n

**Local Access:**
- URL: http://localhost:5678
- Username: `admin`
- Password: `secure_n8n_admin_2024`

**Remote Access (after Tailscale setup):**
- Get your Tailscale IP: `./manage.sh ip`
- URL: `http://YOUR_TAILSCALE_IP:5678`
- Same credentials as above

## 🛠️ Management Commands

Use the `manage.sh` script for easy management:

```bash
./manage.sh start     # Start n8n services
./manage.sh stop      # Stop n8n services  
./manage.sh restart   # Restart n8n services
./manage.sh logs      # View live logs
./manage.sh status    # Check container status
./manage.sh ip        # Show Tailscale IP
```

## 🔧 Configuration Details

### Docker Services
- **n8n**: Latest n8n container with health checks
- **postgres**: PostgreSQL 15 with persistent storage
- **Network**: Isolated bridge network for security
- **Volumes**: Persistent storage for data and workflows

### Security Features
- Basic authentication enabled
- Internal network isolation
- Secure database credentials
- Tailscale VPN for remote access

### Auto-Start Configuration
- macOS LaunchDaemon: `/Library/LaunchDaemons/com.n8n.docker.plist`
- Starts automatically on system boot
- User-level service (runs as your user)

## 📱 Mobile Access with Termius

1. Install Tailscale on your mobile device
2. Sign in with the same account
3. Install Termius app
4. Add your Tailscale IP as a host
5. Access n8n web interface in mobile browser: `http://TAILSCALE_IP:5678`

## 🔍 Troubleshooting

### Check Container Status
```bash
./manage.sh status
```

### View Logs
```bash
./manage.sh logs
```

### Restart Services
```bash
./manage.sh restart
```

### Check Tailscale Connection
```bash
/opt/homebrew/bin/tailscale status
```

### Test n8n Health
```bash
curl http://localhost:5678/healthz
```

## 🔄 Self-Healing Features

- **Container Restart Policy**: `unless-stopped`
- **Health Checks**: Both n8n and PostgreSQL monitored
- **Auto Recovery**: Failed containers automatically restart
- **Boot Persistence**: Services start on system reboot

## 📊 Monitoring

- Health endpoints: `http://localhost:5678/healthz`
- Container logs: `./manage.sh logs`
- Service status: `./manage.sh status`
- Startup logs: Check `~/n8n-self-hosted/n8n-startup*.log`

## 🛡️ Security Considerations

- Change default passwords in `docker-compose.yml`
- Keep Docker and n8n images updated
- Monitor logs for suspicious activity
- Use strong Tailscale authentication

## 📝 Next Steps After Setup

1. Complete Tailscale setup following `tailscale-setup.md`
2. Run `./complete-setup.sh` to finalize configuration
3. Test mobile access via Tailscale
4. Create your first n8n workflow
5. Consider setting up backup procedures for Docker volumes

## 🤝 Support

- n8n Documentation: https://docs.n8n.io/
- Tailscale Documentation: https://tailscale.com/kb/
- Docker Compose Reference: https://docs.docker.com/compose/
