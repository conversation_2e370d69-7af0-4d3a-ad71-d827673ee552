# n8n API Control Documentation

This guide shows you how to control your self-hosted n8n instance programmatically using the REST API.

## Your Setup

- **n8n Instance**: `http://localhost:5678`
- **API Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ` *(Keep this secure!)*
- **API Base URL**: `http://localhost:5678/api/v1`

## Quick Start

### 1. Using the Bash Script

```bash
# Source the functions
source n8n_api_control.sh

# List workflows
get_workflows

# Get specific workflow
get_workflow <workflow_id>

# Create workflow from JSON file
create_workflow workflow.json

# Activate workflow (requires trigger nodes)
curl -X POST -H "X-N8N-API-KEY: $N8N_API_KEY" \
     http://localhost:5678/api/v1/workflows/<workflow_id>/activate

# Get executions
get_executions

# Show status
get_workflow_status
```

### 2. Using Python

```python
from n8n_client import N8nClient

# Initialize client
client = N8nClient()  # Uses N8N_API_KEY environment variable

# Test connection
if client.test_connection():
    print("Connected!")
    
    # Get workflows
    workflows = client.get_workflows()
    
    # Get status
    status = client.get_status()
    print(status)
```

### 3. Using cURL directly

```bash
# Set your API key
export N8N_API_KEY="your_api_key_here"

# List workflows
curl -H "X-N8N-API-KEY: $N8N_API_KEY" \
     http://localhost:5678/api/v1/workflows

# Create workflow
curl -X POST -H "X-N8N-API-KEY: $N8N_API_KEY" \
     -H "Content-Type: application/json" \
     -d @workflow.json \
     http://localhost:5678/api/v1/workflows

# Get executions
curl -H "X-N8N-API-KEY: $N8N_API_KEY" \
     http://localhost:5678/api/v1/executions
```

## Key API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/workflows` | List all workflows |
| GET    | `/workflows/{id}` | Get specific workflow |
| POST   | `/workflows` | Create workflow |
| PUT    | `/workflows/{id}` | Update workflow |
| DELETE | `/workflows/{id}` | Delete workflow |
| POST   | `/workflows/{id}/activate` | Activate workflow |
| POST   | `/workflows/{id}/deactivate` | Deactivate workflow |
| GET    | `/executions` | List executions |
| GET    | `/executions/{id}` | Get specific execution |

## Common Operations

### Create a Workflow

1. **Prepare JSON**: Create a workflow JSON file (see `example_workflow.json`)
2. **Upload**: Use the API to create the workflow
3. **Activate**: If it has trigger nodes, activate it

```bash
# Using the bash script
create_workflow my_workflow.json
```

### Monitor Executions

```bash
# Get recent executions
get_executions

# Get specific execution details
get_execution <execution_id>
```

### Manage Workflow State

```bash
# Activate (requires trigger/webhook/polling nodes)
curl -X POST -H "X-N8N-API-KEY: $N8N_API_KEY" \
     http://localhost:5678/api/v1/workflows/{id}/activate

# Deactivate
curl -X POST -H "X-N8N-API-KEY: $N8N_API_KEY" \
     http://localhost:5678/api/v1/workflows/{id}/deactivate
```

## Important Notes

1. **Manual Execution**: Workflows with just "Start" nodes can't be executed via API - they need trigger nodes (HTTP webhook, scheduled trigger, etc.)

2. **Authentication**: Always include the `X-N8N-API-KEY` header with your requests

3. **Content-Type**: Use `application/json` for POST/PUT requests with JSON data

4. **Security**: Keep your API key secure and never commit it to version control

## Tools Included

- **`n8n_api_control.sh`**: Bash functions for n8n API operations
- **`n8n_client.py`**: Python class for programmatic access
- **`example_workflow.json`**: Sample workflow for testing

## Example: Complete Workflow Lifecycle

```bash
# 1. Create workflow
create_workflow example_workflow.json

# 2. List workflows to get ID
get_workflows

# 3. Activate (if it has triggers)
# Manual workflows can't be activated without trigger nodes

# 4. Monitor executions
get_executions

# 5. Update workflow (modify JSON and re-upload)
update_workflow <id> updated_workflow.json

# 6. Delete when done
delete_workflow <id>
```

This setup allows you to fully automate n8n workflow management, deployment, and monitoring through scripts or applications.
