<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.n8n.docker</string>
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>-c</string>
        <string>cd /Users/<USER>/n8n-self-hosted && export PATH="/Users/<USER>/.local/bin:$PATH" && docker compose up -d</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>UserName</key>
    <string>igorganapolsky</string>
    <key>WorkingDirectory</key>
    <string>/Users/<USER>/n8n-self-hosted</string>
    <key>StandardOutPath</key>
    <string>/Users/<USER>/n8n-self-hosted/n8n-startup.log</string>
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/n8n-self-hosted/n8n-startup-error.log</string>
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/Users/<USER>/.local/bin:/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin</string>
    </dict>
</dict>
</plist>
