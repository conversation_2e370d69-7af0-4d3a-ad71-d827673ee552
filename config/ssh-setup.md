# SSH Access Setup for Tailscale + Termius

This guide will help you set up SSH access to your Mac through Tailscale for use with Termius or any SSH client.

## 🔧 Step 1: Enable SSH on macOS (GUI Method)

Since command-line enabling requires additional permissions, use the System Settings:

1. **Open System Settings**
   - Click Apple menu → System Settings
   - Or press `Cmd+Space`, type "System Settings"

2. **Navigate to Sharing**
   - Click "General" in the sidebar
   - Click "Sharing"

3. **Enable Remote Login**
   - Find "Remote Login" in the list
   - Toggle it **ON**
   - You'll see "Remote Login: On" and your SSH access details

4. **Note the SSH connection info**
   - It will show: `ssh username@ip-address`
   - The username should be: `igorganapolsky`

## 🔧 Step 2: Alternative Command Line Method

If you prefer command line (requires admin privileges):

```bash
# Enable SSH service
sudo launchctl load -w /System/Library/LaunchDaemons/ssh.plist

# Or try this method
sudo systemsetup -setremotelogin on
```

## 🔐 Step 3: Improve SSH Security (Optional but Recommended)

Create a custom SSH config for better security:

```bash
# Create SSH config backup
sudo cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

# Create custom config
sudo tee /etc/ssh/sshd_config.d/custom.conf << EOF
# Enhanced security settings
PermitRootLogin no
PasswordAuthentication yes
ChallengeResponseAuthentication no
UsePAM yes
X11Forwarding yes
PrintMotd no
AcceptEnv LANG LC_*
Subsystem sftp /usr/libexec/sftp-server
EOF

# Restart SSH service
sudo launchctl unload /System/Library/LaunchDaemons/ssh.plist
sudo launchctl load /System/Library/LaunchDaemons/ssh.plist
```

## 📱 Step 4: Configure Termius

After Tailscale is set up and you have your Tailscale IP:

### Get Your Tailscale IP:
```bash
/opt/homebrew/bin/tailscale ip -4
```

### Termius Configuration:
1. **Open Termius app** on your mobile device
2. **Add New Host**:
   - **Alias**: `n8n-server` (or any name you prefer)
   - **Hostname**: Your Tailscale IP (e.g., `100.x.x.x`)
   - **Username**: `igorganapolsky`
   - **Password**: Your Mac login password
   - **Port**: `22` (default SSH port)

3. **Save and Connect**

## 🔍 Step 5: Test SSH Connection

### From another device on Tailscale network:
```bash
ssh igorganapolsky@YOUR_TAILSCALE_IP
```

### Test locally first:
```bash
ssh igorganapolsky@localhost
```

## 🛠️ Troubleshooting

### Check if SSH is running:
```bash
sudo launchctl list | grep ssh
# Should show: com.openssh.sshd
```

### Check SSH service status:
```bash
sudo lsof -i :22
# Should show sshd listening on port 22
```

### Check SSH logs:
```bash
sudo tail -f /var/log/system.log | grep sshd
```

### Test SSH connection locally:
```bash
ssh -v igorganapolsky@localhost
```

## 🔐 Step 6: SSH Key Authentication (Advanced - Optional)

For enhanced security, you can set up SSH key authentication:

### Generate SSH key pair on your mobile device (if supported) or another computer:
```bash
ssh-keygen -t ed25519 -C "<EMAIL>"
```

### Copy public key to your Mac:
```bash
ssh-copy-id igorganapolsky@YOUR_TAILSCALE_IP
```

### Or manually add to authorized_keys:
```bash
mkdir -p ~/.ssh
echo "your-public-key-here" >> ~/.ssh/authorized_keys
chmod 700 ~/.ssh
chmod 600 ~/.ssh/authorized_keys
```

## 📋 Quick Reference

**Your SSH Details:**
- **Username**: `igorganapolsky`
- **Local IP**: `localhost` or `127.0.0.1`
- **Tailscale IP**: Run `./manage.sh ip` to get it
- **Port**: `22`
- **Authentication**: Password (your Mac login password)

**Termius Quick Setup:**
1. Host: Your Tailscale IP
2. Username: igorganapolsky
3. Password: Your Mac password
4. Port: 22

## 🎯 What You Can Do via SSH

Once connected via Termius/SSH:
- Manage n8n: `cd ~/n8n-self-hosted && ./manage.sh status`
- View logs: `./manage.sh logs`
- Restart services: `./manage.sh restart`
- Check system resources: `top`, `htop`, `df -h`
- Edit files: `nano`, `vim`
- Run any terminal commands

## 🔄 Integration with n8n Management

You can create shortcuts in Termius to quickly run n8n commands:

**Termius Snippets:**
- n8n status: `cd ~/n8n-self-hosted && ./manage.sh status`
- n8n logs: `cd ~/n8n-self-hosted && ./manage.sh logs`
- n8n restart: `cd ~/n8n-self-hosted && ./manage.sh restart`
