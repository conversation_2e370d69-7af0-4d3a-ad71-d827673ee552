# Tailscale Setup Instructions

## Manual Setup Required

Since Tailscale requires GUI interaction for initial setup, please follow these steps:

1. **Open Tailscale App**
   - Open Spotlight Search (Cmd+Space)
   - Type "Tailscale" and press Enter
   - The Tailscale app should launch

2. **Sign in to Tailscale**
   - Click "Sign in" in the Tailscale app
   - This will open your web browser
   - Sign in with your Tailscale account (or create one if needed)
   - If you don't have a Tailscale account, go to https://tailscale.com/start

3. **Authorize the device**
   - After signing in, authorize this Mac device
   - Give it a meaningful name like "n8n-server"

4. **Verify Connection**
   - Run: `/opt/homebrew/bin/tailscale status`
   - You should see your device listed with an IP address

5. **Install Tailscale on Mobile Device**
   - Install Tailscale app on your mobile device
   - Sign in with the same account
   - Both devices should now be connected

## After Tailscale Setup

Once Tailscale is connected, you can access n8n from anywhere using your Tailscale IP:

1. Find your Tailscale IP: `/opt/homebrew/bin/tailscale ip -4`
2. Access n8n at: `http://YOUR_TAILSCALE_IP:5678`
3. Login credentials:
   - Username: admin
   - Password: secure_n8n_admin_2024

## Next Steps

After Tailscale is working, run the following to continue setup:
```bash
cd ~/n8n-self-hosted
./complete-setup.sh
```
