#!/bin/bash

# 🤖 AUTOMATED RUNNER
# This script runs the X.com automation with proper environment

# Load credentials from .env if exists
if [ -f "/Users/<USER>/n8n-self-hosted/.env" ]; then
    export $(cat /Users/<USER>/n8n-self-hosted/.env | grep -E "^X_" | xargs)
fi

# Override with command line or environment
export X_USERNAME="${X_USERNAME:-<PERSON><PERSON><PERSON><PERSON><PERSON>}"

# Check if password is set
if [ -z "$X_PASSWORD" ]; then
    echo "❌ X_PASSWORD not set!"
    echo "Please set: export X_PASSWORD='your_password'"
    exit 1
fi

# Run the automation
echo "🚀 Starting affiliate automation..."
echo "📅 Time: $(date)"
echo "👤 User: $X_USERNAME"
echo ""

# Run the Python script
/usr/bin/python3 /Users/<USER>/n8n-self-hosted/ultimate_x_automation.py

# Check exit code
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Automation completed successfully!"
else
    echo ""
    echo "❌ Automation failed - check logs"
fi

echo "📊 Log: /Users/<USER>/n8n-self-hosted/automation_log.json"
