#!/bin/bash

# 🚀 LIVE AUTOMATION WITH YOUR CREDENTIALS
# This is your money-making machine!

export X_USERNAME='Igor<PERSON><PERSON><PERSON>sky'
export X_PASSWORD='Rockland2025&*'

echo "======================================================================"
echo "          💰 AFFILIATE MONEY MAKER - LIVE POSTING"
echo "======================================================================"
echo ""
echo "👤 Account: $X_USERNAME"
echo "🔐 Password: ******* (secured)"
echo "📅 Time: $(date)"
echo ""
echo "🚀 POSTING YOUR AFFILIATE LINK NOW..."
echo ""

# Run the automation
/usr/bin/python3 /Users/<USER>/n8n-self-hosted/ultimate_x_automation.py

if [ $? -eq 0 ]; then
    echo ""
    echo "======================================================================"
    echo "                  🎉 SUCCESS! POST IS LIVE!"
    echo "======================================================================"
    echo ""
    echo "✅ Your affiliate post has been published to X.com!"
    echo "📱 Check it out: https://x.com/IgorGanapolsky"
    echo "💰 Every click = potential commission!"
    echo ""
    echo "📊 This will run automatically:"
    echo "   • 9:00 AM daily"
    echo "   • 12:00 PM daily"
    echo "   • 5:00 PM daily"
    echo ""
    echo "🤑 Start counting your money!"
else
    echo "⚠️ Post may need manual verification - check X.com"
fi
