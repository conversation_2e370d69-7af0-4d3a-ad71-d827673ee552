#!/bin/bash

# 🚀 ACTIVATE AFFILIATE MARKETING EMPIRE
# This script will get your affiliate bot fully operational

set -e

echo "🚀 ACTIVATING YOUR AFFILIATE MARKETING EMPIRE..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "core/docker-compose.yml" ]; then
    print_error "Please run this script from the n8n-self-hosted directory"
    exit 1
fi

print_status "Starting affiliate empire activation..."

# Step 1: Check n8n service status
print_info "Step 1: Checking n8n service status..."
cd core
if ./manage.sh status | grep -q "Up"; then
    print_status "n8n service is running"
else
    print_warning "n8n service not running, starting it..."
    ./manage.sh start
    sleep 10
fi

# Step 2: Check workflow status
print_info "Step 2: Checking workflow status..."
WORKFLOW_COUNT=$(docker exec n8n-self-hosted-postgres-1 psql -U n8n -d n8n -t -c "SELECT COUNT(*) FROM workflow_entity WHERE active = true;" 2>/dev/null | tr -d ' ')
ACTIVE_WORKFLOW=$(docker exec n8n-self-hosted-postgres-1 psql -U n8n -d n8n -t -c "SELECT name FROM workflow_entity WHERE active = true;" 2>/dev/null | tr -d ' ')

if [ "$WORKFLOW_COUNT" -gt 0 ]; then
    print_status "Found $WORKFLOW_COUNT active workflow(s): $ACTIVE_WORKFLOW"
else
    print_warning "No active workflows found"
fi

# Step 3: Get fresh API key
print_info "Step 3: Getting fresh API key..."
print_warning "You need to manually get a fresh API key from n8n:"
echo "1. Open http://localhost:5678"
echo "2. Login with: admin / secure_n8n_admin_2024"
echo "3. Go to Settings → API Keys"
echo "4. Create new API key"
echo "5. Copy the key and update your scripts"

# Step 4: Check required credentials
print_info "Step 4: Checking required credentials..."
print_warning "You need to configure these credentials in n8n:"
echo "✅ X.com API (you provided these)"
echo "❌ Google Sheets API"
echo "❌ Claude API"
echo "❌ Bitly API"
echo "❌ Apify API"

# Step 5: Test Python environment
print_info "Step 5: Testing Python environment..."
cd ../scripts
if python3 -c "import requests, json" 2>/dev/null; then
    print_status "Python dependencies are available"
else
    print_warning "Installing Python dependencies..."
    pip3 install -r ../core/requirements_affiliate_bot.txt
fi

# Step 6: Check if affiliate bot can run
print_info "Step 6: Testing affiliate bot..."
if [ -f "affiliate_monitor.py" ]; then
    print_status "Affiliate monitor script found"
    print_info "You can run: python3 affiliate_monitor.py"
else
    print_error "Affiliate monitor script not found"
fi

# Step 7: Show next steps
echo ""
echo "🎯 NEXT STEPS TO ACTIVATE YOUR EMPIRE:"
echo "======================================"
echo ""
echo "1. 🔑 GET API KEY:"
echo "   - Open http://localhost:5678"
echo "   - Go to Settings → API Keys"
echo "   - Create new key and copy it"
echo ""
echo "2. ⚙️  CONFIGURE CREDENTIALS:"
echo "   - In n8n, go to Credentials"
echo "   - Add X.com API credentials (you have these)"
echo "   - Add Google Sheets API credentials"
echo "   - Add Claude API credentials"
echo "   - Add Bitly API credentials"
echo "   - Add Apify API credentials"
echo ""
echo "3. 🚀 TEST THE SYSTEM:"
echo "   cd scripts/"
echo "   python3 test-affiliate-bot.py"
echo ""
echo "4. 📊 MONITOR PERFORMANCE:"
echo "   python3 affiliate_monitor.py"
echo ""

# Step 8: Show current status
print_info "Current System Status:"
echo "=========================="
cd ../core
./manage.sh status

echo ""
print_status "Your affiliate empire is ready to be activated!"
print_warning "Follow the steps above to complete the setup"
echo ""
print_info "For help, check the README.md file"
