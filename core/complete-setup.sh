#!/bin/bash

# N8N Self-Hosted Complete Setup Script
# This script completes the n8n setup after Tailscale is configured

set -e

echo "🚀 Completing n8n self-hosted setup..."

# Check if Tailscale is working
echo "📡 Checking Tailscale status..."
if ! /opt/homebrew/bin/tailscale status > /dev/null 2>&1; then
    echo "❌ Tailscale is not connected. Please follow the instructions in tailscale-setup.md first."
    exit 1
fi

# Get Tailscale IP
TAILSCALE_IP=$(/opt/homebrew/bin/tailscale ip -4)
echo "✅ Tailscale connected with IP: $TAILSCALE_IP"

# Install the launch daemon for auto-start
echo "🔧 Setting up auto-start service..."
sudo cp ~/n8n-self-hosted/com.n8n.docker.plist /Library/LaunchDaemons/
sudo chown root:wheel /Library/LaunchDaemons/com.n8n.docker.plist
sudo chmod 644 /Library/LaunchDaemons/com.n8n.docker.plist

# Load the launch daemon
sudo launchctl load /Library/LaunchDaemons/com.n8n.docker.plist

echo "✅ Auto-start service configured"

# Check n8n health
echo "🏥 Checking n8n health..."
if curl -f -s http://localhost:5678/healthz > /dev/null; then
    echo "✅ n8n is healthy and running"
else
    echo "⚠️  n8n may still be starting up. Please check docker compose logs if needed."
fi

# Create a management script
cat > ~/n8n-self-hosted/manage.sh << 'EOF'
#!/bin/bash

# N8N Management Script

case "$1" in
    start)
        cd ~/n8n-self-hosted
        export PATH="$HOME/.local/bin:$PATH"
        docker compose up -d
        echo "n8n started"
        ;;
    stop)
        cd ~/n8n-self-hosted
        export PATH="$HOME/.local/bin:$PATH"
        docker compose down
        echo "n8n stopped"
        ;;
    restart)
        cd ~/n8n-self-hosted
        export PATH="$HOME/.local/bin:$PATH"
        docker compose restart
        echo "n8n restarted"
        ;;
    logs)
        cd ~/n8n-self-hosted
        export PATH="$HOME/.local/bin:$PATH"
        docker compose logs -f
        ;;
    status)
        cd ~/n8n-self-hosted
        export PATH="$HOME/.local/bin:$PATH"
        docker compose ps
        ;;
    ip)
        /opt/homebrew/bin/tailscale ip -4
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|logs|status|ip}"
        echo "  start   - Start n8n services"
        echo "  stop    - Stop n8n services"
        echo "  restart - Restart n8n services"
        echo "  logs    - View n8n logs"
        echo "  status  - Show container status"
        echo "  ip      - Show Tailscale IP"
        exit 1
        ;;
esac
EOF

chmod +x ~/n8n-self-hosted/manage.sh

echo ""
echo "🎉 Setup Complete!"
echo ""
echo "📋 Summary:"
echo "  • n8n is running on port 5678"
echo "  • Database: PostgreSQL with persistent storage"
echo "  • Auto-restart: Enabled for self-healing"
echo "  • Auto-start: Will start on system boot"
echo "  • Tailscale IP: $TAILSCALE_IP"
echo ""
echo "🔑 n8n Login:"
echo "  • Local URL: http://localhost:5678"
echo "  • Remote URL: http://$TAILSCALE_IP:5678"
echo "  • Username: admin"
echo "  • Password: secure_n8n_admin_2024"
echo ""
echo "🛠️  Management Commands:"
echo "  • ./manage.sh start    - Start services"
echo "  • ./manage.sh stop     - Stop services"
echo "  • ./manage.sh restart  - Restart services"
echo "  • ./manage.sh logs     - View logs"
echo "  • ./manage.sh status   - Check status"
echo "  • ./manage.sh ip       - Show Tailscale IP"
echo ""
echo "📱 Mobile Access:"
echo "  1. Install Tailscale on your mobile device"
echo "  2. Sign in with the same account"
echo "  3. Access n8n at: http://$TAILSCALE_IP:5678"
echo ""
echo "🔧 Files created:"
echo "  • ~/n8n-self-hosted/docker-compose.yml"
echo "  • ~/n8n-self-hosted/manage.sh"
echo "  • /Library/LaunchDaemons/com.n8n.docker.plist"
echo ""
