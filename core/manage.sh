#!/bin/bash

# N8N Management Script

case "$1" in
    start)
        cd ~/n8n-self-hosted
        export PATH="$HOME/.local/bin:$PATH"
        docker compose up -d
        echo "n8n started"
        ;;
    stop)
        cd ~/n8n-self-hosted
        export PATH="$HOME/.local/bin:$PATH"
        docker compose down
        echo "n8n stopped"
        ;;
    restart)
        cd ~/n8n-self-hosted
        export PATH="$HOME/.local/bin:$PATH"
        docker compose restart
        echo "n8n restarted"
        ;;
    logs)
        cd ~/n8n-self-hosted
        export PATH="$HOME/.local/bin:$PATH"
        docker compose logs -f
        ;;
    status)
        cd ~/n8n-self-hosted
        export PATH="$HOME/.local/bin:$PATH"
        docker compose ps
        ;;
    ip)
        /opt/homebrew/bin/tailscale ip -4
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|logs|status|ip}"
        echo "  start   - Start n8n services"
        echo "  stop    - Stop n8n services"
        echo "  restart - Restart n8n services"
        echo "  logs    - View n8n logs"
        echo "  status  - Show container status"
        echo "  ip      - Show Tailscale IP"
        exit 1
        ;;
esac
