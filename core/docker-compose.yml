version: '3.8'

x-shared-environment: &shared-environment
  # Load environment variables from .env file
  env_file: .env
  # Security context
  security_opt:
    - no-new-privileges:true
  read_only: true
  tmpfs:
    - /tmp
    - /run
    - /var/run

services:
  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-n8n}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-${POSTGRES_PASSWORD_FILE}}
      - POSTGRES_DB=${POSTGRES_DB:-n8n}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./config/postgres/initdb.d:/docker-entrypoint-initdb.d
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U $${POSTGRES_USER:-n8n} -d $${POSTGRES_DB:-n8n}']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - n8n_network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
    logging:
      driver: json-file
      options:
        max-size: 10m
        max-file: "3"

  n8n:
    image: n8nio/n8n:1.0.0
    restart: unless-stopped
    ports:
      - "${N8N_PORT:-5678}:5678"
    environment:
      - N8N_HOST=0.0.0.0
      - N8N_PORT=${N8N_PORT:-5678}
      - N8N_PROTOCOL=${N8N_PROTOCOL:-http}
      - NODE_ENV=production
      - WEBHOOK_URL=${WEBHOOK_URL:-http://localhost:5678/}
      - GENERIC_TIMEZONE=${TZ:-America/New_York}
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=${POSTGRES_DB:-n8n}
      - DB_POSTGRESDB_USER=${POSTGRES_USER:-n8n}
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD:-${POSTGRES_PASSWORD_FILE}}
      - N8N_LOG_LEVEL=${LOG_LEVEL:-info}
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER:-admin}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD:-${N8N_BASIC_AUTH_PASSWORD_FILE}}
      - N8N_SECURE_COOKIE=${N8N_SECURE_COOKIE:-false}
      - N8N_METRICS=true
      - N8N_COMMUNITY_PACKAGES=n8n-nodes-serpapi,@n8n/n8n-nodes-apify
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./config/n8n/nodes.json:/home/<USER>/.n8n/config/nodes.json
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
    logging:
      driver: json-file
      options:
        max-size: 10m
        max-file: "3"
      test: ['CMD-SHELL', 'wget --no-verbose --tries=1 --spider http://localhost:5678/healthz || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - n8n_network

volumes:
  postgres_data:
  n8n_data:

networks:
  n8n_network:
    driver: bridge
