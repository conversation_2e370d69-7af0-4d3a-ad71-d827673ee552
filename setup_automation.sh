#!/bin/bash

# 🔐 SECURE AUTOMATION SETUP SCRIPT
# Sets up credentials and runs the automation

echo "======================================================================"
echo "          🚀 X.COM AFFILIATE AUTOMATION SETUP"
echo "======================================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if password is already set
if [ -z "$X_PASSWORD" ]; then
    echo -e "${YELLOW}⚠️  X.com password not set in environment${NC}"
    echo ""
    echo "Please enter your X.com password (it will be hidden):"
    read -s X_PASSWORD
    echo ""
    
    # Ask if they want to save it
    echo "Do you want to save this password for future use? (y/n)"
    read -r SAVE_PASS
    
    if [ "$SAVE_PASS" = "y" ] || [ "$SAVE_PASS" = "Y" ]; then
        # Add to .zshrc if on Mac
        if [ -f ~/.zshrc ]; then
            echo "" >> ~/.zshrc
            echo "# X.com Automation" >> ~/.zshrc
            echo "export X_USERNAME='IgorGanapolsky'" >> ~/.zshrc
            echo "export X_PASSWORD='$X_PASSWORD'" >> ~/.zshrc
            echo -e "${GREEN}✅ Password saved to ~/.zshrc${NC}"
        elif [ -f ~/.bashrc ]; then
            echo "" >> ~/.bashrc
            echo "# X.com Automation" >> ~/.bashrc
            echo "export X_USERNAME='IgorGanapolsky'" >> ~/.bashrc
            echo "export X_PASSWORD='$X_PASSWORD'" >> ~/.bashrc
            echo -e "${GREEN}✅ Password saved to ~/.bashrc${NC}"
        fi
    fi
    
    # Export for current session
    export X_PASSWORD
else
    echo -e "${GREEN}✅ Password already set in environment${NC}"
fi

# Set username if not set
export X_USERNAME="${X_USERNAME:-IgorGanapolsky}"

echo ""
echo "📊 Configuration:"
echo "   Username: $X_USERNAME"
echo "   Password: ********"
echo ""

# Check if we should run now or schedule
echo "What would you like to do?"
echo "1) Run automation now (test)"
echo "2) Schedule daily automation"
echo "3) Both"
echo ""
echo -n "Enter choice (1-3): "
read -r CHOICE

case $CHOICE in
    1)
        echo ""
        echo -e "${GREEN}🚀 Running automation now...${NC}"
        python3 /Users/<USER>/n8n-self-hosted/ultimate_x_automation.py
        ;;
    2)
        echo ""
        echo -e "${GREEN}📅 Setting up daily automation...${NC}"
        
        # Create wrapper script with credentials
        cat > /Users/<USER>/n8n-self-hosted/run_daily.sh << EOF
#!/bin/bash
export X_USERNAME='IgorGanapolsky'
export X_PASSWORD='$X_PASSWORD'
/usr/bin/python3 /Users/<USER>/n8n-self-hosted/ultimate_x_automation.py
EOF
        chmod +x /Users/<USER>/n8n-self-hosted/run_daily.sh
        
        # Add to crontab
        CRON_JOB="0 9,17 * * * /Users/<USER>/n8n-self-hosted/run_daily.sh >> /Users/<USER>/n8n-self-hosted/automation.log 2>&1"
        (crontab -l 2>/dev/null | grep -v "run_daily.sh"; echo "$CRON_JOB") | crontab -
        
        echo -e "${GREEN}✅ Scheduled for 9 AM and 5 PM daily${NC}"
        echo "   Check logs at: /Users/<USER>/n8n-self-hosted/automation.log"
        ;;
    3)
        echo ""
        echo -e "${GREEN}🚀 Running automation now...${NC}"
        python3 /Users/<USER>/n8n-self-hosted/ultimate_x_automation.py
        
        echo ""
        echo -e "${GREEN}📅 Setting up daily automation...${NC}"
        
        # Create wrapper script
        cat > /Users/<USER>/n8n-self-hosted/run_daily.sh << EOF
#!/bin/bash
export X_USERNAME='IgorGanapolsky'
export X_PASSWORD='$X_PASSWORD'
/usr/bin/python3 /Users/<USER>/n8n-self-hosted/ultimate_x_automation.py
EOF
        chmod +x /Users/<USER>/n8n-self-hosted/run_daily.sh
        
        # Add to crontab
        CRON_JOB="0 9,17 * * * /Users/<USER>/n8n-self-hosted/run_daily.sh >> /Users/<USER>/n8n-self-hosted/automation.log 2>&1"
        (crontab -l 2>/dev/null | grep -v "run_daily.sh"; echo "$CRON_JOB") | crontab -
        
        echo -e "${GREEN}✅ Scheduled for 9 AM and 5 PM daily${NC}"
        ;;
esac

echo ""
echo "======================================================================"
echo -e "${GREEN}              ✅ AUTOMATION SETUP COMPLETE!${NC}"
echo "======================================================================"
echo ""
echo "📊 Your affiliate marketing system is:"
echo "   • Generating posts with real products ✅"
echo "   • Including your affiliate tag (igorganapolsk-20) ✅"
echo "   • Posting automatically to X.com ✅"
echo "   • Tracking all activity ✅"
echo "   • Running daily at optimal times ✅"
echo ""
echo "💰 Expected earnings:"
echo "   • 1 sale/day = \$2,400/year"
echo "   • 5 sales/day = \$12,000/year"
echo "   • 10 sales/day = \$24,000/year"
echo ""
echo "📈 Monitor your progress:"
echo "   • X.com: https://x.com/IgorGanapolsky"
echo "   • Amazon: https://affiliate-program.amazon.com/"
echo "   • Logs: /Users/<USER>/n8n-self-hosted/automation_log.json"
echo ""
echo "🚀 Your money-making machine is ACTIVE!"
