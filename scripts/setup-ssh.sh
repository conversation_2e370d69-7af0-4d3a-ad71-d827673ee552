#!/bin/bash

# SSH Setup Script for n8n Self-Hosted Access
# This script configures SSH for secure remote access via Tailscale

set -e

echo "🔧 Setting up SSH access for n8n server..."

# Check if SSH is already running
if sudo lsof -i :22 > /dev/null 2>&1; then
    echo "✅ SSH service is already running"
else
    echo "📡 Starting SSH service..."
    sudo launchctl load -w /System/Library/LaunchDaemons/ssh.plist
    sleep 2
    
    if sudo lsof -i :22 > /dev/null 2>&1; then
        echo "✅ SSH service started successfully"
    else
        echo "❌ Failed to start SSH service"
        echo "Please enable SSH manually in System Settings > General > Sharing > Remote Login"
        exit 1
    fi
fi

# Create SSH directory if it doesn't exist
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# Create or update known_hosts for localhost
echo "🔐 Setting up SSH host keys..."
if [ ! -f ~/.ssh/known_hosts ]; then
    touch ~/.ssh/known_hosts
    chmod 600 ~/.ssh/known_hosts
fi

# Add localhost to known hosts
ssh-keyscan -H localhost >> ~/.ssh/known_hosts 2>/dev/null || true
ssh-keyscan -H 127.0.0.1 >> ~/.ssh/known_hosts 2>/dev/null || true

# Test SSH connection
echo "🧪 Testing SSH connection..."
if timeout 10 ssh -o BatchMode=yes -o ConnectTimeout=5 igorganapolsky@localhost echo "SSH working" 2>/dev/null; then
    echo "✅ SSH connection test successful"
else
    echo "⚠️  SSH connection test failed - this is normal for first setup"
    echo "You may need to accept the connection interactively first"
fi

# Create enhanced SSH config for security
echo "🛡️  Creating secure SSH configuration..."
sudo tee /etc/ssh/sshd_config.d/n8n-security.conf > /dev/null << 'EOF'
# n8n Server SSH Security Configuration
PermitRootLogin no
MaxAuthTries 3
MaxSessions 10
PasswordAuthentication yes
PubkeyAuthentication yes
PermitEmptyPasswords no
ChallengeResponseAuthentication no
UsePAM yes
X11Forwarding yes
PrintMotd no
AcceptEnv LANG LC_*
Subsystem sftp /usr/libexec/sftp-server

# Rate limiting
ClientAliveInterval 300
ClientAliveCountMax 2
EOF

# Restart SSH to apply configuration
echo "🔄 Applying SSH configuration..."
sudo launchctl unload /System/Library/LaunchDaemons/ssh.plist
sudo launchctl load -w /System/Library/LaunchDaemons/ssh.plist
sleep 2

# Get current IP addresses
echo "📋 Network Information:"
echo "  Local IP: 127.0.0.1"
LOCAL_IP=$(ifconfig | grep "inet " | grep -v "127.0.0.1" | awk '{print $2}' | head -1)
if [ ! -z "$LOCAL_IP" ]; then
    echo "  Network IP: $LOCAL_IP"
fi

# Check if Tailscale is available
if command -v /opt/homebrew/bin/tailscale >/dev/null 2>&1; then
    if /opt/homebrew/bin/tailscale status >/dev/null 2>&1; then
        TAILSCALE_IP=$(/opt/homebrew/bin/tailscale ip -4)
        echo "  Tailscale IP: $TAILSCALE_IP"
    else
        echo "  Tailscale: Not connected (run complete setup first)"
    fi
else
    echo "  Tailscale: Available after complete setup"
fi

# Update the management script to include SSH IP command
if [ -f ~/n8n-self-hosted/manage.sh ]; then
    # Check if ssh-ip command already exists
    if ! grep -q "ssh-ip" ~/n8n-self-hosted/manage.sh; then
        # Add ssh-ip command to manage.sh
        sed -i '' '/ip)/a\
    ssh-ip)\
        echo "SSH Connection Details:"\
        echo "  Username: igorganapolsky"\
        echo "  Port: 22"\
        echo "  Local: ssh igorganapolsky@localhost"\
        if command -v /opt/homebrew/bin/tailscale >/dev/null 2>&1 && /opt/homebrew/bin/tailscale status >/dev/null 2>&1; then\
            TAILSCALE_IP=$(/opt/homebrew/bin/tailscale ip -4)\
            echo "  Tailscale: ssh igorganapolsky@$TAILSCALE_IP"\
        else\
            echo "  Tailscale: Not available (complete Tailscale setup first)"\
        fi\
        ;;' ~/n8n-self-hosted/manage.sh
        
        # Add ssh-ip to usage
        sed -i '' 's/echo "  ip      - Show Tailscale IP"/echo "  ip      - Show Tailscale IP"\
        echo "  ssh-ip  - Show SSH connection details"/' ~/n8n-self-hosted/manage.sh
    fi
fi

echo ""
echo "🎉 SSH Setup Complete!"
echo ""
echo "📋 SSH Connection Details:"
echo "  Username: igorganapolsky"
echo "  Password: Your Mac login password"
echo "  Port: 22 (default)"
echo ""
echo "🔗 Connection Commands:"
echo "  Local: ssh igorganapolsky@localhost"
if [ ! -z "$LOCAL_IP" ]; then
    echo "  Network: ssh igorganapolsky@$LOCAL_IP"
fi
if [ ! -z "$TAILSCALE_IP" ]; then
    echo "  Tailscale: ssh igorganapolsky@$TAILSCALE_IP"
fi
echo ""
echo "📱 Termius Configuration:"
echo "  Host: Your Tailscale IP (get with: ./manage.sh ssh-ip)"
echo "  Username: igorganapolsky"
echo "  Password: Your Mac login password"
echo "  Port: 22"
echo ""
echo "📖 For detailed setup instructions, see: ssh-setup.md"
echo ""
echo "🔧 Management Commands:"
echo "  ./manage.sh ssh-ip  - Show SSH connection details"
echo "  ./manage.sh status   - Check n8n status"
echo "  ./manage.sh logs     - View n8n logs"
echo ""
