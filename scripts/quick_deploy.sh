#!/bin/bash

# 🚀 QUICK AFFILIATE BOT DEPLOYMENT SCRIPT 🚀
# One-command deployment of your complete affiliate automation

set -e  # Exit on any error

# Configuration
N8N_URL="http://localhost:5678"
API_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ"
WORKFLOW_FILE="affiliate-autoposter.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%H:%M:%S')
    
    case $level in
        "SUCCESS") echo -e "✅ [${timestamp}] ${GREEN}${message}${NC}" ;;
        "ERROR")   echo -e "❌ [${timestamp}] ${RED}${message}${NC}" ;;
        "INFO")    echo -e "ℹ️ [${timestamp}] ${BLUE}${message}${NC}" ;;
        "DEPLOY")  echo -e "🚀 [${timestamp}] ${YELLOW}${message}${NC}" ;;
    esac
}

# Test connection
test_connection() {
    log "INFO" "Testing n8n connection..."
    if curl -s -H "X-N8N-API-KEY: $API_KEY" "$N8N_URL/api/v1/workflows" > /dev/null; then
        log "SUCCESS" "Connected to n8n successfully!"
        return 0
    else
        log "ERROR" "Failed to connect to n8n API"
        return 1
    fi
}

# Deploy workflow
deploy_workflow() {
    log "DEPLOY" "Deploying affiliate automation workflow..."
    
    if [ ! -f "$WORKFLOW_FILE" ]; then
        log "ERROR" "Workflow file $WORKFLOW_FILE not found!"
        return 1
    fi
    
    # Deploy the workflow
    RESPONSE=$(curl -s -X POST \
        -H "X-N8N-API-KEY: $API_KEY" \
        -H "Content-Type: application/json" \
        -d @"$WORKFLOW_FILE" \
        "$N8N_URL/api/v1/workflows")
    
    # Extract workflow ID
    WORKFLOW_ID=$(echo "$RESPONSE" | jq -r '.id // empty')
    
    if [ -n "$WORKFLOW_ID" ]; then
        log "SUCCESS" "Workflow deployed! ID: $WORKFLOW_ID"
        echo "$WORKFLOW_ID" > .affiliate_workflow_id
        return 0
    else
        log "ERROR" "Deployment failed: $RESPONSE"
        return 1
    fi
}

# Activate workflow
activate_workflow() {
    if [ ! -f ".affiliate_workflow_id" ]; then
        log "ERROR" "No workflow ID found!"
        return 1
    fi
    
    WORKFLOW_ID=$(cat .affiliate_workflow_id)
    log "DEPLOY" "Activating daily automation..."
    
    RESPONSE=$(curl -s -X POST \
        -H "X-N8N-API-KEY: $API_KEY" \
        "$N8N_URL/api/v1/workflows/$WORKFLOW_ID/activate")
    
    if echo "$RESPONSE" | grep -q '"active": true\|"message": "Workflow.*activated"' || [ -z "$RESPONSE" ]; then
        log "SUCCESS" "🎯 AFFILIATE BOT IS NOW LIVE!"
        log "SUCCESS" "📅 Will post daily at 9 AM"
        return 0
    else
        log "ERROR" "Activation failed: $RESPONSE"
        return 1
    fi
}

# Show status
show_status() {
    if [ ! -f ".affiliate_workflow_id" ]; then
        log "ERROR" "No workflow deployed yet!"
        return 1
    fi
    
    WORKFLOW_ID=$(cat .affiliate_workflow_id)
    
    echo
    log "INFO" "=============================================="
    log "SUCCESS" "🎯 AFFILIATE AUTOPOSTER STATUS"
    log "INFO" "=============================================="
    log "INFO" "📋 Workflow ID: $WORKFLOW_ID"
    log "INFO" "🌐 n8n URL: $N8N_URL"
    log "INFO" "📅 Schedule: Daily at 9:00 AM"
    log "INFO" "🏪 Source: Amazon Best Sellers"
    log "INFO" "🎯 Target: X (Twitter)"
    log "INFO" "📊 Tracking: Google Sheets"
    log "INFO" "🔄 Cooldown: 30 days per product"
    echo
    log "INFO" "🔧 AUTOMATION FEATURES:"
    log "INFO" "  • Daily category rotation (Electronics, Office, Home)"
    log "INFO" "  • Amazon product scraping via Apify"
    log "INFO" "  • 30-day repost cooldown prevention"
    log "INFO" "  • AI-generated posts via Claude"
    log "INFO" "  • URL shortening with Bitly"
    log "INFO" "  • Automatic X posting with images"
    log "INFO" "  • Complete Google Sheets logging"
    echo
    log "INFO" "🚀 ACCESS YOUR WORKFLOW:"
    log "INFO" "   $N8N_URL/workflow/$WORKFLOW_ID"
    echo
}

# Check executions
check_executions() {
    log "INFO" "Checking recent executions..."
    
    EXECUTIONS=$(curl -s -H "X-N8N-API-KEY: $API_KEY" \
        "$N8N_URL/api/v1/executions?limit=5")
    
    if echo "$EXECUTIONS" | jq -r '.data[]' > /dev/null 2>&1; then
        COUNT=$(echo "$EXECUTIONS" | jq -r '.data | length')
        log "SUCCESS" "Found $COUNT recent executions"
        
        # Show recent executions for our workflow if they exist
        if [ -f ".affiliate_workflow_id" ]; then
            WORKFLOW_ID=$(cat .affiliate_workflow_id)
            echo "$EXECUTIONS" | jq -r --arg wid "$WORKFLOW_ID" '.data[] | select(.workflowId == $wid) | "  📊 \(.startedAt[:10]) - Status: \(.status)"' || true
        fi
    else
        log "INFO" "No recent executions found"
    fi
}

# Main deployment function
main() {
    echo
    log "DEPLOY" "🚀 STARTING AFFILIATE BUSINESS DEPLOYMENT"
    log "INFO" "=================================================="
    
    # Step 1: Test connection
    if ! test_connection; then
        exit 1
    fi
    
    # Step 2: Deploy workflow
    if ! deploy_workflow; then
        exit 1
    fi
    
    # Step 3: Activate workflow
    if ! activate_workflow; then
        exit 1
    fi
    
    # Step 4: Show status
    show_status
    
    # Step 5: Check executions
    check_executions
    
    echo
    log "SUCCESS" "🎉 DEPLOYMENT COMPLETE! Your affiliate bot is running!"
    echo
    log "INFO" "🎯 NEXT STEPS:"
    log "INFO" "1. Verify your Google Sheets are set up correctly"
    log "INFO" "2. Check your API credentials are working"
    log "INFO" "3. Monitor the first few executions"
    log "INFO" "4. Run './quick_deploy.sh --status' to check performance"
}

# Handle command line arguments
case "${1:-}" in
    "--status")
        show_status
        check_executions
        ;;
    "--monitor")
        check_executions
        ;;
    *)
        main
        ;;
esac
