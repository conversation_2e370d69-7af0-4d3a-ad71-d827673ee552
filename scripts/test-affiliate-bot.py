#!/usr/bin/env python3
"""
🧪 Test Affiliate Bot
Simple test script to verify affiliate bot functionality
"""

import json
import requests
from datetime import datetime
import sys
from pathlib import Path

def test_basic_functionality():
    """Test basic affiliate bot functionality"""
    print("🧪 Testing Affiliate Bot Basic Functionality...")
    print("=" * 50)
    
    # Test 1: Check if required files exist
    print("✅ Test 1: Checking required files...")
    required_files = [
        "full_automation_affiliate_bot.py",
        "affiliate_monitor.py",
        "deploy_affiliate_bot.py"
    ]
    
    for file in required_files:
        if Path(file).exists():
            print(f"   ✅ {file} - Found")
        else:
            print(f"   ❌ {file} - Missing")
    
    # Test 2: Check if affiliate bot can import
    print("\n✅ Test 2: Testing imports...")
    try:
        sys.path.append('.')
        from full_automation_affiliate_bot import FullyAutomatedAffiliateBot
        print("   ✅ FullyAutomatedAffiliateBot class imported successfully")
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
    
    # Test 3: Test affiliate bot initialization
    print("\n✅ Test 3: Testing bot initialization...")
    try:
        bot = FullyAutomatedAffiliateBot()
        print("   ✅ Affiliate bot initialized successfully")
        print(f"   ✅ Amazon tag: {bot.amazon_tag}")
        print(f"   ✅ Products loaded: {len(bot.products)}")
    except Exception as e:
        print(f"   ❌ Initialization failed: {e}")
    
    # Test 4: Check product data
    print("\n✅ Test 4: Checking product data...")
    try:
        bot = FullyAutomatedAffiliateBot()
        for i, product in enumerate(bot.products[:3]):  # Show first 3 products
            print(f"   📦 Product {i+1}: {product['title']}")
            print(f"      💰 Price: {product['price']}")
            print(f"      💸 Commission: ${product['commission']}")
            print(f"      🏷️  Category: {product['category']}")
            print()
    except Exception as e:
        print(f"   ❌ Product data check failed: {e}")
    
    # Test 5: Test affiliate URL generation
    print("✅ Test 5: Testing affiliate URL generation...")
    try:
        bot = FullyAutomatedAffiliateBot()
        test_asin = "B0BSHF7LLL"
        affiliate_url = f"https://www.amazon.com/dp/{test_asin}?tag={bot.amazon_tag}"
        print(f"   🔗 Generated URL: {affiliate_url}")
        print("   ✅ Affiliate URL generation working")
    except Exception as e:
        print(f"   ❌ URL generation failed: {e}")

def test_n8n_connection():
    """Test connection to n8n"""
    print("\n🔌 Testing n8n Connection...")
    print("=" * 50)
    
    try:
        # Test basic connectivity
        response = requests.get("http://localhost:5678/healthz", timeout=5)
        if response.status_code == 200:
            print("✅ n8n health check: OK")
        else:
            print(f"❌ n8n health check failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to n8n: {e}")
        print("   Make sure n8n is running: cd core && ./manage.sh status")
    
    # Test web interface
    try:
        response = requests.get("http://localhost:5678", timeout=5)
        if response.status_code == 200:
            print("✅ n8n web interface: Accessible")
        else:
            print(f"❌ n8n web interface: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot access n8n web interface: {e}")

def show_next_steps():
    """Show next steps to get the bot working"""
    print("\n🎯 Next Steps to Activate Your Affiliate Empire:")
    print("=" * 60)
    print()
    print("1. 🔑 Get Fresh n8n API Key:")
    print("   - Open http://localhost:5678")
    print("   - Login: admin / secure_n8n_admin_2024")
    print("   - Go to Settings → API Keys")
    print("   - Create new API key")
    print()
    print("2. ⚙️  Configure Credentials in n8n:")
    print("   ✅ X.com API (you have these)")
    print("   ❌ Google Sheets API")
    print("   ❌ Claude API")
    print("   ❌ Bitly API")
    print("   ❌ Apify API")
    print()
    print("3. 🚀 Test the Full System:")
    print("   python3 affiliate_monitor.py")
    print()
    print("4. 📊 Monitor Performance:")
    print("   python3 affiliate_monitor.py --live")

def main():
    """Main test function"""
    print("🚀 AFFILIATE BOT TEST SUITE")
    print("=" * 50)
    print(f"🕐 Test run at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    test_basic_functionality()
    test_n8n_connection()
    show_next_steps()
    
    print("\n" + "=" * 50)
    print("🧪 Test Suite Complete!")
    print("Follow the next steps above to activate your affiliate empire! 🎯")

if __name__ == "__main__":
    main()
