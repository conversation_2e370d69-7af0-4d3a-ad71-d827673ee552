#!/usr/bin/env python3
"""
🚀 AFFILIATE AUTOPOSTER DEPLOYMENT SCRIPT 🚀
Deploy your complete affiliate business automation to n8n programmatically!

Features:
- Deploys your Amazon affiliate workflow
- Updates credential references
- Activates daily automation
- Sets up monitoring
- Creates performance tracking
"""

import requests
import json
import time
from datetime import datetime, timedelta
import os

# Configuration
N8N_URL = "http://localhost:5678"
API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ"
WORKFLOW_FILE = "affiliate-autoposter.json"

class AffiliateBot:
    def __init__(self):
        self.headers = {
            "X-N8N-API-KEY": API_KEY,
            "Content-Type": "application/json"
        }
        self.workflow_id = None
        
    def log(self, message, level="INFO"):
        """Enhanced logging with timestamps"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        icons = {"INFO": "ℹ️", "SUCCESS": "✅", "ERROR": "❌", "DEPLOY": "🚀"}
        print(f"{icons.get(level, 'ℹ️')} [{timestamp}] {message}")
    
    def test_connection(self):
        """Test n8n API connection"""
        try:
            response = requests.get(f"{N8N_URL}/api/v1/workflows", headers=self.headers)
            if response.status_code == 200:
                self.log("Connected to n8n successfully!", "SUCCESS")
                return True
            else:
                self.log(f"Connection failed: {response.status_code}", "ERROR")
                return False
        except Exception as e:
            self.log(f"Connection error: {e}", "ERROR")
            return False
    
    def load_workflow(self):
        """Load the affiliate workflow from JSON"""
        try:
            with open(WORKFLOW_FILE, 'r') as f:
                workflow = json.load(f)
            self.log(f"Loaded workflow: {workflow['name']}", "SUCCESS")
            return workflow
        except Exception as e:
            self.log(f"Failed to load workflow: {e}", "ERROR")
            return None
    
    def update_credential_references(self, workflow):
        """Update credential IDs to match your n8n instance"""
        self.log("Updating credential references...")
        
        # Credential mapping for your setup
        credential_mapping = {
            "NjK0bszo3le4otnv": "NjK0bszo3le4otnv",  # Your Google Sheets credential
            "**********************************************": "**********************************************",  # Apify
            "************************************************************************************************************": "************************************************************************************************************",  # Claude
            "e0caf30921d055534608494f6ab068d251fd2eed": "e0caf30921d055534608494f6ab068d251fd2eed",  # Bitly
            "4nyJwIueb1AiO6M6bAq4fRWqG": "4nyJwIueb1AiO6M6bAq4fRWqG"  # Twitter/X
        }
        
        # Update nodes with credential references
        for node in workflow['nodes']:
            if 'credentials' in node:
                for cred_type, cred_id in node['credentials'].items():
                    if cred_id in credential_mapping:
                        node['credentials'][cred_type] = credential_mapping[cred_id]
                        self.log(f"Updated {cred_type} credential for {node['name']}")
        
        return workflow
    
    def deploy_workflow(self, workflow):
        """Deploy workflow to n8n"""
        self.log("Deploying affiliate automation workflow...", "DEPLOY")
        
        try:
            response = requests.post(
                f"{N8N_URL}/api/v1/workflows",
                headers=self.headers,
                json=workflow
            )
            
            if response.status_code == 200 or response.status_code == 201:
                result = response.json()
                self.workflow_id = result['id']
                self.log(f"Workflow deployed! ID: {self.workflow_id}", "SUCCESS")
                return result
            else:
                self.log(f"Deployment failed: {response.text}", "ERROR")
                return None
                
        except Exception as e:
            self.log(f"Deployment error: {e}", "ERROR")
            return None
    
    def activate_workflow(self):
        """Activate the workflow to start daily automation"""
        if not self.workflow_id:
            self.log("No workflow ID found!", "ERROR")
            return False
        
        self.log("Activating daily automation...", "DEPLOY")
        
        try:
            response = requests.post(
                f"{N8N_URL}/api/v1/workflows/{self.workflow_id}/activate",
                headers=self.headers
            )
            
            if response.status_code == 200:
                self.log("🎯 AFFILIATE BOT IS NOW LIVE!", "SUCCESS")
                self.log("📅 Will post daily at 9 AM", "SUCCESS")
                return True
            else:
                self.log(f"Activation failed: {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Activation error: {e}", "ERROR")
            return False
    
    def test_manual_execution(self):
        """Test manual execution to verify everything works"""
        if not self.workflow_id:
            return False
        
        self.log("Testing workflow execution...")
        
        try:
            # Note: This workflow uses a cron trigger, so manual execution might not work
            # Let's check the workflow status instead
            response = requests.get(
                f"{N8N_URL}/api/v1/workflows/{self.workflow_id}",
                headers=self.headers
            )
            
            if response.status_code == 200:
                workflow_data = response.json()
                active_status = workflow_data.get('active', False)
                self.log(f"Workflow status: {'ACTIVE' if active_status else 'INACTIVE'}")
                return True
            else:
                self.log(f"Status check failed: {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Test error: {e}", "ERROR")
            return False
    
    def get_recent_executions(self):
        """Get recent executions to monitor performance"""
        self.log("Checking recent executions...")
        
        try:
            response = requests.get(
                f"{N8N_URL}/api/v1/executions?limit=5",
                headers=self.headers
            )
            
            if response.status_code == 200:
                executions = response.json().get('data', [])
                self.log(f"Found {len(executions)} recent executions")
                
                for execution in executions[:3]:  # Show last 3
                    status = execution.get('status', 'unknown')
                    started = execution.get('startedAt', '')
                    workflow_name = execution.get('workflowId', '')
                    
                    # Check if this is our workflow
                    if workflow_name == self.workflow_id:
                        self.log(f"  📊 {started[:10]} - Status: {status}")
                
                return executions
            else:
                self.log(f"Failed to get executions: {response.text}", "ERROR")
                return []
                
        except Exception as e:
            self.log(f"Execution check error: {e}", "ERROR")
            return []
    
    def show_workflow_summary(self):
        """Display complete workflow summary"""
        self.log("=" * 60, "INFO")
        self.log("🎯 AFFILIATE AUTOPOSTER DEPLOYMENT SUMMARY", "SUCCESS")
        self.log("=" * 60, "INFO")
        
        if self.workflow_id:
            self.log(f"📋 Workflow ID: {self.workflow_id}")
            self.log(f"🌐 n8n URL: {N8N_URL}")
            self.log("📅 Schedule: Daily at 9:00 AM")
            self.log("🏪 Source: Amazon Best Sellers")
            self.log("🎯 Target: X (Twitter)")
            self.log("📊 Tracking: Google Sheets")
            self.log("🔄 Cooldown: 30 days per product")
            
            # Show what the workflow does
            self.log("\n🔧 AUTOMATION FEATURES:")
            self.log("  • Daily category rotation (Electronics, Office, Home)")
            self.log("  • Amazon product scraping via Apify")
            self.log("  • 30-day repost cooldown prevention")
            self.log("  • AI-generated posts via Claude")
            self.log("  • URL shortening with Bitly")
            self.log("  • Automatic X posting with images")
            self.log("  • Complete Google Sheets logging")
            
            self.log(f"\n🚀 ACCESS YOUR WORKFLOW:")
            self.log(f"   {N8N_URL}/workflow/{self.workflow_id}")
            
            self.log("\n📈 MONITORING:")
            self.log("   Run this script with --monitor to check performance")
            
        else:
            self.log("❌ Deployment failed - no workflow ID", "ERROR")
    
    def deploy_complete_system(self):
        """Deploy the complete affiliate system"""
        self.log("🚀 STARTING AFFILIATE BUSINESS DEPLOYMENT", "DEPLOY")
        self.log("=" * 50, "INFO")
        
        # Step 1: Test connection
        if not self.test_connection():
            return False
        
        # Step 2: Load workflow
        workflow = self.load_workflow()
        if not workflow:
            return False
        
        # Step 3: Update credentials
        workflow = self.update_credential_references(workflow)
        
        # Step 4: Deploy workflow
        result = self.deploy_workflow(workflow)
        if not result:
            return False
        
        # Step 5: Activate workflow
        if not self.activate_workflow():
            return False
        
        # Step 6: Test and verify
        self.test_manual_execution()
        
        # Step 7: Check recent executions
        self.get_recent_executions()
        
        # Step 8: Show complete summary
        self.show_workflow_summary()
        
        self.log("🎉 DEPLOYMENT COMPLETE! Your affiliate bot is running!", "SUCCESS")
        return True

def main():
    """Main deployment function"""
    bot = AffiliateBot()
    
    # Check if monitoring mode
    if len(os.sys.argv) > 1 and os.sys.argv[1] == "--monitor":
        if bot.test_connection():
            bot.get_recent_executions()
    else:
        # Full deployment
        success = bot.deploy_complete_system()
        if not success:
            print("\n❌ Deployment failed! Check the logs above.")
            exit(1)
        
        print("\n🎯 NEXT STEPS:")
        print("1. Verify your Google Sheets are set up correctly")
        print("2. Check your API credentials are working")
        print("3. Monitor the first few executions")
        print("4. Run 'python deploy_affiliate_bot.py --monitor' to check performance")

if __name__ == "__main__":
    main()
