#!/usr/bin/env python3
"""
Ollama Agent - A simple agent that can execute commands based on <PERSON>llama's suggestions
WARNING: This gives AI the ability to execute commands on your system!
"""

import subprocess
import json
import sys
import os

def run_ollama(prompt, model="llama3.1:8b"):
    """Send a prompt to Ollama and get response"""
    cmd = f"echo '{prompt}' | ollama run {model}"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    return result.stdout.strip()

def execute_command(command):
    """Execute a shell command and return output"""
    print(f"\n🤖 Executing: {command}")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return f"Output:\n{result.stdout}\n{result.stderr if result.stderr else ''}"
    except Exception as e:
        return f"Error: {str(e)}"

def agent_loop():
    """Main agent loop with auto-execution"""
    print("🚨 OLLAMA AGENT - FULL AUTO MODE 🚨")
    print("⚠️  WARNING: This will execute commands automatically!")
    print("Press Ctrl+C to stop\n")
    
    model = "llama3.1:8b"  # or "qwen2.5-coder:latest" for coding tasks
    
    while True:
        try:
            user_input = input("\n💬 You: ")
            if user_input.lower() in ['exit', 'quit', 'bye']:
                print("👋 Goodbye!")
                break
            
            # Ask Ollama to provide a command
            prompt = f"""You are a helpful assistant that can execute terminal commands.
User request: {user_input}

Respond with ONLY the command to execute, nothing else. If multiple commands are needed, separate with &&.
If no command is needed, respond with 'NO_COMMAND'.

Command:"""
            
            response = run_ollama(prompt, model)
            print(f"\n🤖 Ollama suggests: {response}")
            
            if response and response != "NO_COMMAND" and not response.startswith("NO_COMMAND"):
                # Auto-execute the command
                result = execute_command(response)
                print(f"\n📊 Result: {result}")
                
                # Feed result back to Ollama for interpretation
                interpret_prompt = f"The command '{response}' was executed with this result:\n{result}\n\nProvide a brief summary of what happened."
                interpretation = run_ollama(interpret_prompt, model)
                print(f"\n🔍 Interpretation: {interpretation}")
            else:
                print("ℹ️  No command to execute.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Stopped by user")
            break
        except Exception as e:
            print(f"\n❌ Error: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--yolo":
        print("🎲 YOLO MODE ACTIVATED - NO SAFETY CHECKS!")
        agent_loop()
    else:
        print("Run with --yolo flag to enable full auto-execution mode")
        print("Example: python3 ollama_agent.py --yolo")
