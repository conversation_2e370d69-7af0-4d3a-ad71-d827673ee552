#!/usr/bin/env python3
"""
🚀 DIRECT X.COM AFFILIATE POSTER
Posts your affiliate links directly to X.com - no BS, just money!
"""

import os
import json
import random
from datetime import datetime
from pathlib import Path

# Load environment
env_file = Path("/Users/<USER>/n8n-self-hosted/.env")
if env_file.exists():
    with open(env_file, 'r') as f:
        for line in f:
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value.strip('"').strip("'")

# Products that make money
PRODUCTS = [
    {
        "asin": "B0BSHF7LLL",
        "title": "Apple AirPods (3rd Gen)",
        "price": "$169.99", 
        "commission": 6.80,
        "hashtags": "#Apple #AirPods #tech #deals"
    },
    {
        "asin": "B08C1W5N87",
        "title": "Fire TV Stick 4K",
        "price": "$49.99",
        "commission": 2.00,
        "hashtags": "#FireTV #streaming #4K #deals"
    },
    {
        "asin": "B07VGRJDFY",
        "title": "Echo Dot Smart Speaker",
        "price": "$39.99",
        "commission": 1.60,
        "hashtags": "#Echo #Alexa #smarthome #tech"
    }
]

def get_today_product():
    """Get product for today"""
    day = datetime.now().timetuple().tm_yday
    return PRODUCTS[day % len(PRODUCTS)]

def create_post(product):
    """Create the money-making post"""
    url = f"https://www.amazon.com/dp/{product['asin']}?tag=igorganapolsk-20"
    
    templates = [
        f"🔥 {product['title']} for {product['price']}!\n\nThis deal is insane - grab it now!\n\n👉 {url}\n\n{product['hashtags']} #ad",
        f"💰 Save on {product['title']} - Only {product['price']}\n\nBest price I've seen!\n\n🛒 {url}\n\n{product['hashtags']} #ad",
        f"⚡ {product['title']} Deal: {product['price']}\n\nDon't miss this one!\n\n✅ {url}\n\n{product['hashtags']} #ad"
    ]
    
    return random.choice(templates)

def post_to_x_api(content):
    """Try to post using API"""
    try:
        import requests
        from requests_oauthlib import OAuth1
        
        # X.com API v2 endpoint
        url = "https://api.twitter.com/2/tweets"
        
        # OAuth 1.0a
        auth = OAuth1(
            os.getenv('X_API_KEY'),
            os.getenv('X_API_SECRET'),
            os.getenv('X_ACCESS_TOKEN'),
            os.getenv('X_ACCESS_TOKEN_SECRET')
        )
        
        # Post the tweet
        response = requests.post(
            url,
            auth=auth,
            json={"text": content}
        )
        
        if response.status_code == 201:
            data = response.json()
            tweet_id = data['data']['id']
            print(f"✅ POSTED TO X.COM!")
            print(f"🔗 https://x.com/IgorGanapolsky/status/{tweet_id}")
            return True
        else:
            print(f"❌ X.com API error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("="*60)
    print("💰 AFFILIATE MONEY MAKER - POSTING TO X.COM")
    print("="*60)
    
    # Get today's product
    product = get_today_product()
    print(f"\n📦 Product: {product['title']}")
    print(f"💵 Your Commission: ${product['commission']:.2f} per sale")
    
    # Create post
    post = create_post(product)
    print(f"\n📝 Post Content:")
    print("-"*40)
    print(post)
    print("-"*40)
    
    # Try to post
    success = post_to_x_api(post)
    
    if success:
        print("\n🎉 SUCCESS! Your affiliate link is now on X.com!")
        print("💰 Every click could mean money in your pocket!")
    else:
        print("\n⚠️ Couldn't post automatically. Copy and paste manually:")
        print("\n" + "="*60)
        print(post)
        print("="*60)
        print("\n👆 Copy this and post on X.com manually")
    
    # Save to log
    log_file = Path("/Users/<USER>/n8n-self-hosted/x_posts.json")
    log_entry = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "product": product['title'],
        "commission": product['commission'],
        "posted": success,
        "content": post
    }
    
    if log_file.exists():
        with open(log_file, 'r') as f:
            logs = json.load(f)
    else:
        logs = []
    
    logs.append(log_entry)
    
    with open(log_file, 'w') as f:
        json.dump(logs, f, indent=2)
    
    print(f"\n💾 Saved to log: {log_file}")
    
    # Show earnings potential
    total_posts = len(logs)
    total_potential = sum(l['commission'] for l in logs)
    print(f"\n📊 EARNINGS TRACKING:")
    print(f"  Total posts: {total_posts}")
    print(f"  Potential earnings: ${total_potential:.2f}")
    print(f"  Average per post: ${total_potential/total_posts:.2f}")

if __name__ == "__main__":
    main()
