#!/usr/bin/env python3

import json
import subprocess
import time

# Create a minimal working affiliate workflow
def create_minimal_workflow():
    workflow = {
        "name": f"Working Affiliate Bot {int(time.time())}",
        "active": False,
        "nodes": [
            {
                "parameters": {
                    "triggerTimes": [{"mode": "everyDay", "hour": 9}]
                },
                "name": "Daily Trigger",
                "type": "n8n-nodes-base.cron",
                "typeVersion": 1,
                "position": [200, 300],
                "id": "daily-trigger-001"
            },
            {
                "parameters": {
                    "functionCode": """// Pick weekday category
const day = new Date().getDay();
let category;
if ([1,3,5].includes(day)) category = 'Electronics';
else if ([2,4].includes(day)) category = 'Office Products';
else category = 'Home & Kitchen';
return [{ json: { category } }];"""
                },
                "name": "Pick Category",
                "type": "n8n-nodes-base.function",
                "typeVersion": 1,
                "position": [400, 300],
                "id": "pick-category-002"
            },
            {
                "parameters": {
                    "functionCode": """// Mock Amazon Data Generator
const categories = {
  "Electronics": [
    {
      asin: "B0CX23V2ZK",
      title: "Apple AirPods Pro (2nd Generation)",
      price: "$199.99",
      url: "https://www.amazon.com/dp/B0CX23V2ZK?tag=yourtag-20",
      image: "https://m.media-amazon.com/images/I/61SUj2aKoEL._AC_SX679_.jpg"
    },
    {
      asin: "B08N5WRWNW",
      title: "Echo Dot (5th Gen, 2022 release)",
      price: "$39.99",
      url: "https://www.amazon.com/dp/B08N5WRWNW?tag=yourtag-20",
      image: "https://m.media-amazon.com/images/I/714Rq4k05UL._AC_SX679_.jpg"
    }
  ],
  "Office Products": [
    {
      asin: "B08KTZ8249",
      title: "Mechanical Gaming Keyboard RGB",
      price: "$89.99",
      url: "https://www.amazon.com/dp/B08KTZ8249?tag=yourtag-20",
      image: "https://m.media-amazon.com/images/I/71example._AC_SX679_.jpg"
    }
  ],
  "Home & Kitchen": [
    {
      asin: "B0C1M2N3O4",
      title: "Instant Pot Duo 7-in-1 Electric Pressure Cooker",
      price: "$79.99",
      url: "https://www.amazon.com/dp/B0C1M2N3O4?tag=yourtag-20",
      image: "https://m.media-amazon.com/images/I/71example._AC_SX679_.jpg"
    }
  ]
};

const category = $json.category || "Electronics";
const products = categories[category] || categories["Electronics"];
const pick = products[Math.floor(Math.random() * products.length)];

return [{ json: pick }];"""
                },
                "name": "Generate Product Data",
                "type": "n8n-nodes-base.function", 
                "typeVersion": 1,
                "position": [600, 300],
                "id": "generate-data-003"
            },
            {
                "parameters": {
                    "values": {
                        "string": [
                            {"name": "post_text", "value": "🔥 Deal Alert! {{$json.title}} for just {{$json.price}} ⚡ Perfect for developers and WFH setups! Get yours: {{$json.url}} #ad #tech #deals"}
                        ]
                    }
                },
                "name": "Create Simple Post",
                "type": "n8n-nodes-base.set",
                "typeVersion": 3,
                "position": [800, 300],
                "id": "create-post-004"
            }
        ],
        "connections": {
            "Daily Trigger": {
                "main": [
                    [{"node": "Pick Category", "type": "main", "index": 0}]
                ]
            },
            "Pick Category": {
                "main": [
                    [{"node": "Generate Product Data", "type": "main", "index": 0}]
                ]
            },
            "Generate Product Data": {
                "main": [
                    [{"node": "Create Simple Post", "type": "main", "index": 0}]
                ]
            }
        },
        "settings": {}
    }
    
    return workflow

# Save and deploy via curl
workflow = create_minimal_workflow()

with open('/tmp/minimal_workflow.json', 'w') as f:
    json.dump(workflow, f, indent=2)

print("✅ Minimal affiliate workflow created!")
print("🚀 Now deploying via API...")

# Deploy using curl
cmd = [
    'curl', '-s', '-X', 'POST', 'http://localhost:5678/api/v1/workflows',
    '-H', 'X-N8N-API-KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ',
    '-H', 'Content-Type: application/json',
    '-d', '@/tmp/minimal_workflow.json'
]

result = subprocess.run(cmd, capture_output=True, text=True)

if result.returncode == 0:
    try:
        response = json.loads(result.stdout)
        workflow_id = response.get('id')
        is_active = response.get('active', False)
        
        if workflow_id:
            print(f"🎉 SUCCESS! New workflow created: {workflow_id}")
            print(f"📊 Active: {is_active}")
            print(f"🌐 Access: http://localhost:5678/workflow/{workflow_id}")
            
            if not is_active:
                print("🔥 Activating the new workflow...")
                activate_cmd = [
                    'curl', '-s', '-X', 'POST', f'http://localhost:5678/api/v1/workflows/{workflow_id}/activate',
                    '-H', 'X-N8N-API-KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ'
                ]
                activate_result = subprocess.run(activate_cmd, capture_output=True, text=True)
                try:
                    activate_response = json.loads(activate_result.stdout)
                    if activate_response.get('active'):
                        print("✅ YOUR NEW AFFILIATE BOT IS LIVE!")
                        print("📅 Will post daily at 9 AM")
                        print("🤖 Using mock Amazon product data")
                        print("💡 Add Claude, Bitly, X nodes later for full automation")
                except json.JSONDecodeError:
                    print(f"⚠️  Activation failed with non-JSON response: {activate_result.stdout}")
            else:
                print("✅ Workflow is already active!")

        else:
            print("❌ Workflow created but no ID returned")
            print(f"Response: {result.stdout}")
            
    except json.JSONDecodeError:
        print("❌ Invalid JSON response")
        print(f"Response: {result.stdout}")
else:
    print("❌ Failed to create workflow")
    print(f"Error: {result.stderr}")
    print(f"Response: {result.stdout}")
