#!/usr/bin/env python3
"""
🚀 Quick Credential Setup for Affiliate Bot
Sets up Apify and X.com credentials automatically
"""

import requests
import json
from datetime import datetime

# n8n API configuration
N8N_URL = "http://localhost:5678"
API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NjA3ODkyfQ.xRhDWfd3KL-jCWp51sDDIyBhs8TdXLUkAEJqhTSL0VI"

def log(message, level="INFO"):
    """Enhanced logging"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    icons = {"INFO": "ℹ️", "SUCCESS": "✅", "ERROR": "❌", "WARN": "⚠️"}
    print(f"{icons.get(level, 'ℹ️')} [{timestamp}] {message}")

def create_apify_credential():
    """Create Apify credential"""
    headers = {
        "X-N8N-API-KEY": API_KEY,
        "Content-Type": "application/json"
    }
    
    credential = {
        "name": "Apify Affiliate Bot",
        "type": "apify",
        "data": {
            "token": "**********************************************"
        }
    }
    
    try:
        response = requests.post(
            f"{N8N_URL}/api/v1/credentials",
            headers=headers,
            json=credential,
            timeout=10
        )
        
        if response.status_code in [200, 201]:
            result = response.json()
            log(f"✅ Apify credential created: {result.get('id')}", "SUCCESS")
            return result.get('id')
        else:
            log(f"❌ Failed to create Apify credential: {response.status_code}", "ERROR")
            log(f"Response: {response.text}", "ERROR")
            return None
    except Exception as e:
        log(f"❌ Error creating Apify credential: {e}", "ERROR")
        return None

def create_x_credential():
    """Create X.com credential"""
    headers = {
        "X-N8N-API-KEY": API_KEY,
        "Content-Type": "application/json"
    }
    
    credential = {
        "name": "X.com Affiliate Bot",
        "type": "twitterOAuth1Api",
        "data": {
            "consumerKey": "*************************",
            "consumerSecret": "FQkLIPevGC4q5jHihhJdoSxSBdrg9c8zvD32hPvKDN2Z9hS3V8",
            "oauthToken": "1733256637199073280-jI2zj0UlxFU0juop1wn96hZgydh5IS",
            "oauthTokenSecret": "KznFZd7y6NXVdXen58FHup8JI5Z4qp55AAUvrdZtGVA2d"
        }
    }
    
    try:
        response = requests.post(
            f"{N8N_URL}/api/v1/credentials",
            headers=headers,
            json=credential,
            timeout=10
        )
        
        if response.status_code in [200, 201]:
            result = response.json()
            log(f"✅ X.com credential created: {result.get('id')}", "SUCCESS")
            return result.get('id')
        else:
            log(f"❌ Failed to create X.com credential: {response.status_code}", "ERROR")
            log(f"Response: {response.text}", "ERROR")
            return None
    except Exception as e:
        log(f"❌ Error creating X.com credential: {e}", "ERROR")
        return None

def test_connection():
    """Test n8n connection"""
    headers = {"X-N8N-API-KEY": API_KEY}
    
    try:
        response = requests.get(f"{N8N_URL}/api/v1/workflows", headers=headers, timeout=10)
        if response.status_code == 200:
            log("Connected to n8n successfully!", "SUCCESS")
            return True
        else:
            log(f"Connection failed: {response.status_code}", "ERROR")
            return False
    except Exception as e:
        log(f"Connection error: {e}", "ERROR")
        return False

def main():
    """Main function"""
    print("🚀 QUICK CREDENTIAL SETUP FOR AFFILIATE BOT")
    print("=" * 60)
    print(f"🕐 Setup started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test connection
    if not test_connection():
        log("Cannot connect to n8n. Check if it's running.", "ERROR")
        return
    
    # Create credentials
    log("Setting up essential credentials...", "INFO")
    
    apify_id = create_apify_credential()
    x_id = create_x_credential()
    
    # Summary
    print("\n" + "=" * 60)
    log("Setup complete!", "INFO")
    
    if apify_id and x_id:
        log("🎉 Both essential credentials configured!", "SUCCESS")
        log(f"Apify ID: {apify_id}", "INFO")
        log(f"X.com ID: {x_id}", "INFO")
        print()
        log("Your affiliate bot can now:", "SUCCESS")
        log("✅ Scrape Amazon products via Apify", "SUCCESS")
        log("✅ Post to X.com automatically", "SUCCESS")
        print()
        log("🎯 Next steps:", "INFO")
        log("1. Test the bot: python test_active_bot.py", "INFO")
        log("2. Monitor performance: python affiliate_monitor.py", "INFO")
        log("3. Add Claude and Bitly credentials later for full automation", "WARN")
    else:
        log("❌ Some credentials failed to create", "ERROR")
        log("Check the errors above and try again", "WARN")

if __name__ == "__main__":
    main()
