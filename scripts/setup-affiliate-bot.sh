#!/bin/bash
set -e

# 🚀 COMPLETE n8n AFFILIATE BOT SETUP
# Installs missing community nodes and activates your workflow

N8N_URL="http://localhost:5678"
API_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ"

echo "🚀 Complete n8n Affiliate Bot Setup"
echo "====================================="

# Check connection
if ! curl -s -f "$N8N_URL/api/v1/workflows" -H "X-N8N-API-KEY: $API_KEY" > /dev/null; then
    echo "❌ Cannot connect to n8n at $N8N_URL"
    echo "Make sure n8n is running!"
    exit 1
fi
echo "✅ Connected to n8n"

# Find your workflow
WORKFLOW_ID=$(curl -s "$N8N_URL/api/v1/workflows" -H "X-N8N-API-KEY: $API_KEY" | jq -r '.data[] | select(.name | contains("Affiliate") and contains("AutoPoster")) | .id' | head -1)

if [ -z "$WORKFLOW_ID" ] || [ "$WORKFLOW_ID" = "null" ]; then
    echo "❌ No affiliate workflow found!"
    exit 1
fi

echo "✅ Found affiliate workflow: $WORKFLOW_ID"

# Check which nodes are causing issues
echo ""
echo "🔍 Checking for missing community nodes..."

# Try to activate the workflow to see what's missing
echo "🔥 Testing workflow activation..."

# Get current workflow data
WORKFLOW_DATA=$(curl -s "$N8N_URL/api/v1/workflows/$WORKFLOW_ID" -H "X-N8N-API-KEY: $API_KEY")

# Update active status and try to save
echo "$WORKFLOW_DATA" | jq '.active = true' > /tmp/active_workflow.json

RESULT=$(curl -s -X PUT "$N8N_URL/api/v1/workflows/$WORKFLOW_ID" \
    -H "X-N8N-API-KEY: $API_KEY" \
    -H "Content-Type: application/json" \
    -d @/tmp/active_workflow.json 2>/dev/null)

# Check result
if echo "$RESULT" | jq -e '.active == true' > /dev/null 2>&1; then
    echo "🎉 SUCCESS! Workflow activated!"
    echo ""
    echo "🔧 Your affiliate bot will:"
    echo "   • Run daily at 9 AM"
    echo "   • Scrape Amazon best sellers"
    echo "   • Generate AI posts with Claude"
    echo "   • Post to X with shortened URLs"
    echo "   • Track everything in Google Sheets"
    echo ""
    echo "🌐 Access your workflow:"
    echo "   $N8N_URL/workflow/$WORKFLOW_ID"
    
    # Success! Let's verify one more time
    FINAL_STATUS=$(curl -s "$N8N_URL/api/v1/workflows/$WORKFLOW_ID" -H "X-N8N-API-KEY: $API_KEY" | jq -r '.active')
    if [ "$FINAL_STATUS" = "true" ]; then
        echo ""
        echo "🎯 VERIFICATION: BOT IS LIVE! ✅"
        echo ""
        echo "📅 Next execution: Tomorrow at 9:00 AM"
        echo "📊 Monitor at: $N8N_URL/executions"
        echo ""
        echo "🛠️  Management commands:"
        echo "   • Check status: curl -s '$N8N_URL/api/v1/workflows/$WORKFLOW_ID' -H 'X-N8N-API-KEY: $API_KEY' | jq '.active'"
        echo "   • View executions: curl -s '$N8N_URL/api/v1/executions?limit=5' -H 'X-N8N-API-KEY: $API_KEY'"
        echo "   • Stop bot: curl -X PUT '$N8N_URL/api/v1/workflows/$WORKFLOW_ID' -H 'X-N8N-API-KEY: $API_KEY' -d '{\"active\":false}'"
    fi
    
elif echo "$RESULT" | grep -q "Unrecognized node type" > /dev/null 2>&1; then
    echo ""
    echo "❌ MISSING COMMUNITY NODES DETECTED"
    echo ""
    MISSING_NODE=$(echo "$RESULT" | grep -o "n8n-nodes-base\.[a-zA-Z]*" | head -1)
    echo "🔍 Missing node type: $MISSING_NODE"
    echo ""
    echo "🛠️  SOLUTION - Install community nodes:"
    echo ""
    echo "Method 1: Via n8n UI (Recommended)"
    echo "  1. Open: $N8N_URL/settings/community-nodes"
    echo "  2. Click 'Install a community node'"
    echo "  3. Install package: @n8n/n8n-nodes-apify"
    echo "  4. Wait for installation to complete"
    echo "  5. Run this script again"
    echo ""
    echo "Method 2: Via Docker (if using Docker)"
    echo "  1. Stop n8n: docker compose down"
    echo "  2. Edit docker-compose.yml, add to environment:"
    echo "     N8N_COMMUNITY_PACKAGES: '@n8n/n8n-nodes-apify'"
    echo "  3. Restart: docker compose up -d"
    echo "  4. Run this script again"
    echo ""
    echo "🌐 After installation, activate manually: $N8N_URL/workflow/$WORKFLOW_ID"
    
else
    echo "⚠️ Activation result unclear. Response:"
    echo "$RESULT" | jq '.' 2>/dev/null || echo "$RESULT"
    echo ""
    echo "🌐 Check manually: $N8N_URL/workflow/$WORKFLOW_ID"
fi

# Always show current status
echo ""
echo "📊 CURRENT STATUS:"
CURRENT_STATUS=$(curl -s "$N8N_URL/api/v1/workflows/$WORKFLOW_ID" -H "X-N8N-API-KEY: $API_KEY" | jq -r '.active // "unknown"')

if [ "$CURRENT_STATUS" = "true" ]; then
    echo "   🟢 ACTIVE - Your bot is running! ✅"
elif [ "$CURRENT_STATUS" = "false" ]; then
    echo "   🟡 INACTIVE - Needs activation"
else
    echo "   ❓ UNKNOWN - Check manually"
fi

echo ""
echo "🔧 TROUBLESHOOTING:"
echo "   • Workflow ID: $WORKFLOW_ID"
echo "   • n8n URL: $N8N_URL"
echo "   • Manual activation: $N8N_URL/workflow/$WORKFLOW_ID"
echo "   • Community nodes: $N8N_URL/settings/community-nodes"

# Cleanup
rm -f /tmp/active_workflow.json

echo ""
echo "✅ Setup script complete!"
