#!/usr/bin/env python3
"""
DEEP RESEARCH: X.com API Automation Investigation
Finding out exactly why we can't post and how to fix it
"""

import os
import json
import requests
from requests_oauthlib import OAuth1
from pathlib import Path

# Load environment
env_file = Path("/Users/<USER>/n8n-self-hosted/.env")
if env_file.exists():
    with open(env_file, 'r') as f:
        for line in f:
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value.strip('"').strip("'")

def test_api_access():
    """Test what API endpoints we can actually access"""
    
    # Get credentials
    api_key = os.getenv('X_API_KEY')
    api_secret = os.getenv('X_API_SECRET')
    access_token = os.getenv('X_ACCESS_TOKEN')
    access_secret = os.getenv('X_ACCESS_SECRET')
    bearer_token = os.getenv('X_BEARER_TOKEN')
    
    print("="*70)
    print("            🔬 X.COM API DEEP RESEARCH REPORT")
    print("="*70)
    
    print("\n📋 CREDENTIAL STATUS:")
    print(f"  ✓ API Key: {api_key[:10]}..." if api_key else "  ✗ API Key: Missing")
    print(f"  ✓ API Secret: {api_secret[:10]}..." if api_secret else "  ✗ API Secret: Missing")
    print(f"  ✓ Access Token: {access_token[:10]}..." if access_token else "  ✗ Access Token: Missing")
    print(f"  ✓ Access Secret: {access_secret[:10]}..." if access_secret else "  ✗ Access Secret: Missing")
    print(f"  ✓ Bearer Token: {bearer_token[:10]}..." if bearer_token else "  ✗ Bearer Token: Missing")
    
    # Create OAuth1 auth
    auth = OAuth1(api_key, api_secret, access_token, access_secret)
    
    print("\n🔍 TESTING API ENDPOINTS:")
    print("-"*50)
    
    # Test endpoints
    tests = [
        {
            "name": "Get User Info (v1.1)",
            "url": "https://api.twitter.com/1.1/account/verify_credentials.json",
            "method": "GET",
            "auth": auth,
            "headers": None
        },
        {
            "name": "Get User Info (v2 with OAuth)",
            "url": "https://api.twitter.com/2/users/me",
            "method": "GET",
            "auth": auth,
            "headers": None
        },
        {
            "name": "Get User Info (v2 with Bearer)",
            "url": "https://api.twitter.com/2/users/by/username/IgorGanapolsky",
            "method": "GET",
            "auth": None,
            "headers": {"Authorization": f"Bearer {bearer_token}"}
        },
        {
            "name": "Post Tweet (v1.1)",
            "url": "https://api.twitter.com/1.1/statuses/update.json",
            "method": "POST",
            "auth": auth,
            "headers": None,
            "data": {"status": "Test from API"}
        },
        {
            "name": "Post Tweet (v2)",
            "url": "https://api.twitter.com/2/tweets",
            "method": "POST",
            "auth": auth,
            "headers": None,
            "json": {"text": "Test from API v2"}
        },
        {
            "name": "Get Timeline (v1.1)",
            "url": "https://api.twitter.com/1.1/statuses/home_timeline.json",
            "method": "GET",
            "auth": auth,
            "headers": None,
            "params": {"count": 1}
        }
    ]
    
    results = []
    
    for test in tests:
        print(f"\n📍 Testing: {test['name']}")
        print(f"   Endpoint: {test['url']}")
        
        try:
            if test['method'] == 'GET':
                response = requests.get(
                    test['url'],
                    auth=test.get('auth'),
                    headers=test.get('headers'),
                    params=test.get('params', {})
                )
            else:
                response = requests.post(
                    test['url'],
                    auth=test.get('auth'),
                    headers=test.get('headers'),
                    data=test.get('data'),
                    json=test.get('json')
                )
            
            if response.status_code == 200 or response.status_code == 201:
                print(f"   ✅ SUCCESS! Status: {response.status_code}")
                results.append({"endpoint": test['name'], "status": "SUCCESS", "code": response.status_code})
            else:
                print(f"   ❌ FAILED! Status: {response.status_code}")
                error_data = response.json() if response.content else {}
                if 'errors' in error_data:
                    print(f"   Error: {error_data['errors'][0].get('message', 'Unknown')}")
                elif 'detail' in error_data:
                    print(f"   Error: {error_data['detail']}")
                results.append({"endpoint": test['name'], "status": "FAILED", "code": response.status_code, "error": error_data})
                
        except Exception as e:
            print(f"   ⚠️ EXCEPTION: {str(e)}")
            results.append({"endpoint": test['name'], "status": "ERROR", "error": str(e)})
    
    print("\n" + "="*70)
    print("                    📊 RESEARCH FINDINGS")
    print("="*70)
    
    # Analyze results
    can_read = any(r['status'] == 'SUCCESS' and 'Get' in r['endpoint'] for r in results)
    can_post = any(r['status'] == 'SUCCESS' and 'Post' in r['endpoint'] for r in results)
    
    print(f"\n🔍 API ACCESS LEVEL:")
    print(f"  • Read Access: {'✅ YES' if can_read else '❌ NO'}")
    print(f"  • Write Access: {'✅ YES' if can_post else '❌ NO'}")
    
    # Check for specific error patterns
    for r in results:
        if r['status'] == 'FAILED' and 'error' in r:
            if r.get('code') == 403:
                if 'oauth1-permissions' in str(r.get('error', '')):
                    print(f"\n⚠️ ISSUE FOUND: Your app lacks OAuth1 permissions for posting")
                    print(f"   → Solution: Need to upgrade X.com app access level")
                elif 'subset of X API V2' in str(r.get('error', '')):
                    print(f"\n⚠️ ISSUE FOUND: Free tier limitations")
                    print(f"   → Solution: Need Basic or Pro API access ($100-$5000/month)")
    
    print("\n💡 ROOT CAUSE ANALYSIS:")
    print("""
    The issue is that X.com (Twitter) has restricted API access:
    
    1. FREE TIER LIMITATIONS:
       • Only read access to tweets
       • Cannot post tweets
       • Limited to 1,500 tweets/month reading
       • No write access without payment
    
    2. REQUIRED FOR POSTING:
       • Basic tier: $100/month (3,000 posts/month)
       • Pro tier: $5,000/month (300,000 posts/month)
       • Or use OAuth 1.0a with elevated access (deprecated)
    
    3. YOUR CURRENT STATUS:
       • You have valid OAuth credentials
       • But your app is on Free tier
       • Cannot post without upgrading
    """)
    
    return results

def find_automation_solutions():
    """Research alternative automation methods"""
    
    print("\n" + "="*70)
    print("            🚀 AUTOMATION SOLUTIONS AVAILABLE")
    print("="*70)
    
    solutions = """
    
    ✅ SOLUTION 1: BROWSER AUTOMATION (100% FREE)
    ────────────────────────────────────────────
    Use Selenium or Playwright to automate browser:
    • Mimics real user behavior
    • Logs into X.com with your credentials
    • Posts automatically
    • Completely free
    • Works with any account
    
    ✅ SOLUTION 2: THIRD-PARTY SERVICES
    ────────────────────────────────────────────
    Services that still have grandfathered API access:
    • Buffer (Free tier: 3 social accounts)
    • Hootsuite (30-day free trial)
    • IFTTT (Free tier available)
    • Zapier (Free tier: 100 tasks/month)
    • Make.com (formerly Integromat)
    
    ✅ SOLUTION 3: HYBRID APPROACH
    ────────────────────────────────────────────
    • Generate posts automatically (current setup)
    • Use Apple Shortcuts/Automator to paste
    • Schedule with launchd/cron
    • Semi-automated but reliable
    
    ✅ SOLUTION 4: X.COM PRO API ($100/month)
    ────────────────────────────────────────────
    • Official solution
    • 3,000 posts per month
    • Full API access
    • Most reliable but costs money
    
    🎯 RECOMMENDED: Browser Automation (Selenium)
    • 100% free
    • Works immediately
    • No API limitations
    • Can handle any X.com features
    """
    
    print(solutions)
    
    return solutions

if __name__ == "__main__":
    # Run deep research
    test_results = test_api_access()
    solutions = find_automation_solutions()
    
    # Save research report
    report = {
        "timestamp": str(Path.ctime(Path.cwd())),
        "test_results": test_results,
        "solutions": solutions
    }
    
    with open("x_api_research_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print("\n📄 Full report saved to: x_api_research_report.json")
    print("\n🎯 NEXT STEP: Implementing browser automation for 100% free posting!")
