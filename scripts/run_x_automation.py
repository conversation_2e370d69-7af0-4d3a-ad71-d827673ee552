#!/usr/bin/env python3
"""
X.COM FULLY AUTOMATED POSTER
Runs daily to post affiliate links
"""

import os
from x_browser_automation import get_daily_product, create_post_content, post_with_selenium

# Get credentials from environment
USERNAME = os.getenv("X_USERNAME", "<PERSON><PERSON><PERSON><PERSON><PERSON>")
PASSWORD = os.getenv("X_PASSWORD", "YOUR_PASSWORD_HERE")  # Set this!

# Get today's product
product = get_daily_product()

# Create post
content = create_post_content(product)

print(f"Today's post: {content}")

# Post it
if PASSWORD != "YOUR_PASSWORD_HERE":
    success = post_with_selenium(USERNAME, PASSWORD, content)
    if success:
        print("💰 Posted successfully! Check for commissions!")
else:
    print("⚠️ Set your X.com password in the script or environment")
