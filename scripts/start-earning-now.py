#!/usr/bin/env python3
"""
🚀 START EARNING NOW - Manual Trigger Affiliate Bot
Create and run a manual version immediately to start making money
"""

import subprocess
import json

API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ"

def curl_request(method, endpoint, data=None):
    cmd = [
        'curl', '-s', '-X', method,
        f'http://localhost:5678/api/v1/{endpoint}',
        '-H', f'X-N8N-API-KEY: {API_KEY}',
        '-H', 'Content-Type: application/json'
    ]
    
    if data:
        cmd.extend(['-d', json.dumps(data)])
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        try:
            return json.loads(result.stdout)
        except json.JSONDecodeError:
            return {"raw": result.stdout}
    else:
        return {"error": result.stderr}

def main():
    print("🚀 START EARNING NOW!")
    print("💰 Creating manual trigger affiliate bot")
    print("=" * 50)
    print()
    
    # Manual trigger version for immediate execution
    manual_bot = {
        "name": "💰 START EARNING NOW (Manual)",
        "nodes": [
            {
                "parameters": {},
                "name": "Manual Trigger - CLICK TO EARN!",
                "type": "n8n-nodes-base.manualTrigger",
                "typeVersion": 1,
                "position": [200, 300]
            },
            {
                "parameters": {
                    "functionCode": "// HIGH-CONVERTING PRODUCTS - IMMEDIATE EARNINGS\\nconst YOUR_AMAZON_TAG = 'igorganapolsk-20';\\n\\n// Today's featured high-converting products\\nconst todayProducts = [\\n  {\\n    asin: 'B0CHX7R4Q3',\\n    title: 'ASUS ROG Ally Handheld Gaming Console',\\n    price: '$499.99',\\n    commission: 15.00,\\n    category: 'Electronics'\\n  },\\n  {\\n    asin: 'B0CX23V2ZK',\\n    title: 'Apple AirPods Pro (2nd Generation)',\\n    price: '$199.99',\\n    commission: 8.00,\\n    category: 'Electronics'\\n  },\\n  {\\n    asin: 'B08N5WRWNW',\\n    title: 'Echo Dot (5th Gen, 2022 release)',\\n    price: '$39.99',\\n    commission: 1.60,\\n    category: 'Electronics'\\n  },\\n  {\\n    asin: 'B0CL5KNB9M',\\n    title: 'Framework Laptop 16 - Modular Gaming Laptop',\\n    price: '$1399.99',\\n    commission: 35.00,\\n    category: 'Office Products'\\n  }\\n];\\n\\n// Pick a random product\\nconst product = todayProducts[Math.floor(Math.random() * todayProducts.length)];\\n\\n// Create REAL affiliate URL\\nproduct.url = `https://www.amazon.com/dp/${product.asin}?tag=${YOUR_AMAZON_TAG}`;\\n\\nconsole.log('💰 EARNING OPPORTUNITY CREATED!');\\nconsole.log(`Product: ${product.title}`);\\nconsole.log(`Price: ${product.price}`);\\nconsole.log(`YOUR Affiliate URL: ${product.url}`);\\nconsole.log(`Potential Commission: $${product.commission}`);\\nconsole.log(`Amazon Tag: ${YOUR_AMAZON_TAG}`);\\n\\nreturn [{ json: {\\n  ...product,\\n  timestamp: new Date().toISOString(),\\n  ready_to_earn: true\\n}}];"
                },
                "name": "Generate Earnings Opportunity",
                "type": "n8n-nodes-base.function",
                "typeVersion": 1,
                "position": [400, 300]
            },
            {
                "parameters": {
                    "functionCode": "// Create high-converting marketing post\\nconst product = $json;\\n\\nconst powerTemplates = [\\n  `🔥 FOUND IT! ${product.title} for ${product.price} ⚡ This changed my workflow completely! Get yours: ${product.url} #ad #tech`,\\n  `💻 Game changer alert: ${product.title} at ${product.price}! Perfect timing - just what I needed 🚀 ${product.url} #ad #productivity`,\\n  `⚡ Deal of the day: ${product.title} for ${product.price}! Been using this for weeks - absolutely recommend 👀 ${product.url} #ad`,\\n  `🎯 Pro setup: ${product.title} at ${product.price} is incredible value! Perfect for remote work 💰 ${product.url} #ad #deals`,\\n  `🔥 Upgrade alert: ${product.title} for ${product.price}! This is exactly what every developer needs ✨ ${product.url} #ad #wfh`\\n];\\n\\nconst post = powerTemplates[Math.floor(Math.random() * powerTemplates.length)];\\n\\nconsole.log('📱 HIGH-CONVERTING POST GENERATED:');\\nconsole.log(post);\\n\\nreturn [{ json: {\\n  ...product,\\n  post_text: post,\\n  character_count: post.length,\\n  ready_to_share: true\\n}}];"
                },
                "name": "Create Viral Post",
                "type": "n8n-nodes-base.function",
                "typeVersion": 1,
                "position": [600, 300]
            },
            {
                "parameters": {
                    "operation": "append",
                    "sheetId": "1aYCw8uspcH76KfxRwITFs-mz26Z9ouQ9lhuZd8uG144",
                    "range": "Products!A:K",
                    "options": {
                        "valueInputMode": "RAW",
                        "values": {
                            "values": [
                                "={{$now}}",
                                "={{$json.asin}}",
                                "={{$json.title}}",
                                "={{$json.price}}",
                                "4%",
                                "={{$json.url}}",
                                "https://m.media-amazon.com/images/I/61SUj2aKoEL._AC_SX679_.jpg",
                                "={{$json.post_text}}",
                                "TRUE",
                                "0",
                                "={{$json.commission}}"
                            ]
                        }
                    }
                },
                "name": "💰 LOG TO GOOGLE SHEET",
                "type": "n8n-nodes-base.googleSheets",
                "typeVersion": 3,
                "position": [800, 300],
                "credentials": {
                    "googleApi": "NjK0bszo3le4otnv"
                }
            },
            {
                "parameters": {
                    "functionCode": "// SUCCESS & EARNINGS CONFIRMATION\\nconst data = $json;\\n\\nconsole.log('');\\nconsole.log('🎉'.repeat(20));\\nconsole.log('💰 AFFILIATE EARNINGS OPPORTUNITY LIVE!');\\nconsole.log('🎉'.repeat(20));\\nconsole.log('');\\nconsole.log('✅ WHAT JUST HAPPENED:');\\nconsole.log(`📱 Created viral post: ${data.character_count} characters`);\\nconsole.log(`🔗 Generated affiliate URL: ${data.url}`);\\nconsole.log(`📊 Added to Google Sheet successfully`);\\nconsole.log(`💵 Potential commission: $${data.commission}`);\\nconsole.log(`⚡ Amazon Associates tag: igorganapolsk-20`);\\nconsole.log('');\\nconsole.log('🚀 NEXT STEPS TO START EARNING:');\\nconsole.log('1. 📱 Copy the generated post from above');\\nconsole.log('2. 🐦 Share on X/Twitter, LinkedIn, or social media');\\nconsole.log('3. 💰 Every click = potential commission!');\\nconsole.log('4. 📊 Check Google Sheet for tracking');\\nconsole.log('5. 🔄 Run this again for more opportunities');\\nconsole.log('');\\nconsole.log('💡 YOUR AFFILIATE URL IS LIVE AND READY!');\\nconsole.log('💰 START SHARING TO START EARNING!');\\nconsole.log('');\\n\\nreturn [{ json: {\\n  status: 'EARNING_READY',\\n  commission_potential: data.commission,\\n  affiliate_url: data.url,\\n  post_ready: true,\\n  amazon_tag: 'igorganapolsk-20',\\n  action_required: 'Share the post on social media to start earning!'\\n}}];"
                },
                "name": "🎉 EARNINGS ACTIVATED",
                "type": "n8n-nodes-base.function",
                "typeVersion": 1,
                "position": [1000, 300]
            }
        ],
        "connections": {
            "Manual Trigger - CLICK TO EARN!": {
                "main": [["Generate Earnings Opportunity"]]
            },
            "Generate Earnings Opportunity": {
                "main": [["Create Viral Post"]]
            },
            "Create Viral Post": {
                "main": [["💰 LOG TO GOOGLE SHEET"]]
            },
            "💰 LOG TO GOOGLE SHEET": {
                "main": [["🎉 EARNINGS ACTIVATED"]]
            }
        },
        "settings": {}
    }
    
    print("1. Creating manual earning bot...")
    create_result = curl_request('POST', 'workflows', manual_bot)
    
    if 'id' not in create_result:
        print(f"❌ Creation failed: {create_result}")
        return False
    
    bot_id = create_result['id']
    print(f"✅ Manual bot created! ID: {bot_id}")
    print()
    
    print("2. 🚀 EXECUTING IMMEDIATE EARNING OPPORTUNITY...")
    execute_result = curl_request('POST', f'workflows/{bot_id}/execute')
    
    if 'error' not in execute_result:
        print("✅ EXECUTION SUCCESSFUL!")
        print()
        print("🎉 🎉 🎉 YOUR EARNING OPPORTUNITY IS LIVE! 🎉 🎉 🎉")
        print()
        print("✅ WHAT HAPPENED:")
        print("   💰 Generated affiliate link with YOUR tag")
        print("   📱 Created viral marketing post")
        print("   📊 Added to your Google Sheet")
        print("   🚀 Ready to start earning NOW!")
        print()
        print("🔥 IMMEDIATE ACTION REQUIRED:")
        print("1. 📊 Check your Google Sheet - NEW ROW added!")
        print("2. 🐦 Copy the post from n8n execution logs")
        print("3. 📱 Share on X/Twitter, LinkedIn, Reddit, etc.")
        print("4. 💰 Every click = potential commission!")
        print("5. 🔄 Run this bot again for more opportunities")
        print()
        print("🌐 LINKS:")
        print(f"   Bot: http://localhost:5678/workflow/{bot_id}")
        print("   Executions: http://localhost:5678/executions")
        print()
        print("💡 TO KEEP EARNING:")
        print("   • Click 'Manual Trigger' in the bot again")
        print("   • Share each generated post on social media")
        print("   • Watch your Google Sheet fill with opportunities")
        print("   • Check Amazon Associates for commissions")
        
        return True
    else:
        print(f"❌ Execution failed: {execute_result}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print()
        print("🏆 SUCCESS! Your earning opportunity is LIVE!")
        print("💰 Go share that post and start making money!")
    else:
        print("❌ Issues detected - let me fix this")
