#!/bin/bash
set -e

# 🚀 AFFILIATE BOT QUICK DEPLOY SCRIPT
# One-command deployment for your affiliate automation

N8N_URL="http://localhost:5678"
API_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ"

echo "🚀 Affiliate Bot Quick Deploy"
echo "=============================="

# Check if n8n is running
echo "📡 Testing n8n connection..."
if ! curl -s -f "$N8N_URL/api/v1/workflows" -H "X-N8N-API-KEY: $API_KEY" > /dev/null; then
    echo "❌ Cannot connect to n8n at $N8N_URL"
    echo "Make sure n8n is running with: docker compose up -d"
    exit 1
fi
echo "✅ Connected to n8n"

# Check if affiliate workflow already exists and activate it
echo "🔍 Looking for existing affiliate workflow..."
EXISTING_WORKFLOW=$(curl -s "$N8N_URL/api/v1/workflows" -H "X-N8N-API-KEY: $API_KEY" | jq -r '.data[] | select(.name | contains("Affiliate") and contains("AutoPoster")) | .id' | head -1)

if [ ! -z "$EXISTING_WORKFLOW" ] && [ "$EXISTING_WORKFLOW" != "null" ]; then
    echo "✅ Found existing affiliate workflow: $EXISTING_WORKFLOW"
    
    # Activate it
    echo "🔥 Activating workflow..."
    ACTIVATE_RESULT=$(curl -s -X POST "$N8N_URL/api/v1/workflows/$EXISTING_WORKFLOW/activate" \
        -H "X-N8N-API-KEY: $API_KEY" \
        -H "Content-Type: application/json")
    
    if echo "$ACTIVATE_RESULT" | jq -e '.active == true' > /dev/null; then
        echo "🎉 SUCCESS! Your affiliate bot is now LIVE!"
        echo "📋 Workflow ID: $EXISTING_WORKFLOW"
        echo "🌐 Access at: $N8N_URL/workflow/$EXISTING_WORKFLOW"
        echo ""
        echo "🔧 Your bot will:"
        echo "   • Run daily at 9 AM"
        echo "   • Scrape Amazon best sellers"
        echo "   • Generate AI posts with Claude"
        echo "   • Post to X with shortened URLs"
        echo "   • Track everything in Google Sheets"
        echo ""
        echo "📊 Monitor executions:"
        echo "   curl -s '$N8N_URL/api/v1/executions?limit=5' -H 'X-N8N-API-KEY: $API_KEY' | jq '.data[].status'"
        
        # Test execution (optional)
        read -p "🧪 Test the workflow now? (y/N): " test_now
        if [[ $test_now =~ ^[Yy]$ ]]; then
            echo "🧪 Running test execution..."
            TEST_RESULT=$(curl -s -X POST "$N8N_URL/api/v1/workflows/$EXISTING_WORKFLOW/execute" \
                -H "X-N8N-API-KEY: $API_KEY")
            
            if [ $? -eq 0 ]; then
                echo "✅ Test execution started!"
                echo "📊 Check the n8n UI for results"
            else
                echo "⚠️ Test execution might have failed - check n8n UI"
            fi
        fi
        
    else
        echo "❌ Failed to activate workflow"
        echo "Response: $ACTIVATE_RESULT"
        exit 1
    fi
    
else
    echo "❌ No existing affiliate workflow found!"
    echo ""
    echo "📝 To import from JSON file:"
    echo "   python3 deploy_affiliate_bot.py"
    echo ""
    echo "📋 Or create manually in n8n UI at: $N8N_URL"
    exit 1
fi

echo ""
echo "🎯 DEPLOYMENT COMPLETE!"
echo "Your affiliate marketing automation is now running 24/7"
