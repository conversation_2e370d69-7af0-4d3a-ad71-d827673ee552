#!/usr/bin/env python3
"""
n8n API Client
A Python class for controlling n8n programmatically via REST API
"""

import requests
import json
from typing import Dict, List, Optional, Any
import os

class N8nClient:
    def __init__(self, base_url: str = "http://localhost:5678", api_key: str = None):
        """
        Initialize n8n API client
        
        Args:
            base_url: Base URL of your n8n instance
            api_key: Your n8n API key
        """
        self.base_url = base_url.rstrip('/')
        self.api_url = f"{self.base_url}/api/v1"
        
        # Get API key from parameter or environment
        self.api_key = api_key or os.getenv('N8N_API_KEY')
        if not self.api_key:
            raise ValueError("API key must be provided either as parameter or N8N_API_KEY environment variable")
        
        self.headers = {
            'X-N8N-API-KEY': self.api_key,
            'Content-Type': 'application/json'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None) -> Dict:
        """Make HTTP request to n8n API"""
        url = f"{self.api_url}/{endpoint.lstrip('/')}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data)
            elif method.upper() == 'PATCH':
                response = self.session.patch(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            
            # Handle empty responses (like DELETE)
            if response.status_code == 204 or not response.content:
                return {'success': True}
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response: {e.response.text}")
            raise
    
    # Workflow Management
    def get_workflows(self) -> List[Dict]:
        """Get all workflows"""
        result = self._make_request('GET', '/workflows')
        return result.get('data', [])
    
    def get_workflow(self, workflow_id: str) -> Dict:
        """Get specific workflow by ID"""
        return self._make_request('GET', f'/workflows/{workflow_id}')
    
    def create_workflow(self, workflow_data: Dict) -> Dict:
        """Create a new workflow"""
        return self._make_request('POST', '/workflows', workflow_data)
    
    def update_workflow(self, workflow_id: str, workflow_data: Dict) -> Dict:
        """Update existing workflow"""
        return self._make_request('PUT', f'/workflows/{workflow_id}', workflow_data)
    
    def delete_workflow(self, workflow_id: str) -> Dict:
        """Delete workflow"""
        return self._make_request('DELETE', f'/workflows/{workflow_id}')
    
    def activate_workflow(self, workflow_id: str, active: bool = True) -> Dict:
        """Activate or deactivate workflow"""
        return self._make_request('PATCH', f'/workflows/{workflow_id}', {'active': active})
    
    # Workflow Execution
    def execute_workflow(self, workflow_id: str, input_data: Dict = None) -> Dict:
        """Execute a workflow"""
        data = input_data or {}
        return self._make_request('POST', f'/workflows/{workflow_id}/execute', data)
    
    def get_executions(self, limit: int = 20) -> List[Dict]:
        """Get workflow executions"""
        result = self._make_request('GET', f'/executions?limit={limit}')
        return result.get('data', [])
    
    def get_execution(self, execution_id: str) -> Dict:
        """Get specific execution by ID"""
        return self._make_request('GET', f'/executions/{execution_id}')
    
    def stop_execution(self, execution_id: str) -> Dict:
        """Stop a running execution"""
        return self._make_request('POST', f'/executions/{execution_id}/stop')
    
    # Utility Methods
    def get_status(self) -> Dict:
        """Get n8n instance status"""
        workflows = self.get_workflows()
        executions = self.get_executions()
        
        return {
            'workflow_count': len(workflows),
            'execution_count': len(executions),
            'active_workflows': len([w for w in workflows if w.get('active', False)]),
            'api_url': self.api_url
        }
    
    def test_connection(self) -> bool:
        """Test API connection"""
        try:
            self.get_workflows()
            return True
        except Exception as e:
            print(f"Connection test failed: {e}")
            return False

# Example usage
if __name__ == "__main__":
    # Initialize client
    client = N8nClient()
    
    # Test connection
    if client.test_connection():
        print("✅ Connected to n8n successfully!")
        
        # Show status
        status = client.get_status()
        print(f"📊 Status: {json.dumps(status, indent=2)}")
        
        # Get workflows
        workflows = client.get_workflows()
        if workflows:
            print(f"📋 Found {len(workflows)} workflows:")
            for workflow in workflows:
                print(f"  - {workflow.get('name', 'Unnamed')} (ID: {workflow.get('id')})")
        else:
            print("📋 No workflows found")
            
    else:
        print("❌ Failed to connect to n8n API")
        print("Make sure:")
        print("1. n8n is running")
        print("2. API key is correct")
        print("3. Base URL is correct")
