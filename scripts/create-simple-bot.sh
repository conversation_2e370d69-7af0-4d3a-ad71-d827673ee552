#!/bin/bash
set -e

N8N_URL="http://localhost:5678"
API_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ"

echo "🚀 Creating Simple Working Affiliate Bot"
echo "========================================"

# Create minimal workflow JSON
cat > /tmp/simple_bot.json << 'EOF'
{
  "name": "Simple Affiliate Bot (WORKING)",
  "active": true,
  "nodes": [
    {
      "parameters": {
        "triggerTimes": [{"mode": "everyDay", "hour": 9}]
      },
      "name": "Daily Trigger",
      "type": "n8n-nodes-base.cron", 
      "typeVersion": 1,
      "position": [200, 300],
      "id": "trigger-001"
    },
    {
      "parameters": {
        "functionCode": "// Generate affiliate post data\nconst products = [\n  {\n    title: 'Apple AirPods Pro (2nd Gen)',\n    price: '$199.99',\n    url: 'https://amazon.com/dp/B0CX23V2ZK?tag=yourtag-20',\n    post: '🔥 AirPods Pro (2nd Gen) for $199.99! Perfect sound quality for developers 🎧 Get yours: https://amazon.com/dp/B0CX23V2ZK?tag=yourtag-20 #ad #tech'\n  },\n  {\n    title: 'Echo Dot (5th Gen)',\n    price: '$39.99', \n    url: 'https://amazon.com/dp/B08N5WRWNW?tag=yourtag-20',\n    post: '⚡ Echo Dot (5th Gen) only $39.99! Smart assistant for your home office 🏠 https://amazon.com/dp/B08N5WRWNW?tag=yourtag-20 #ad #smart'\n  },\n  {\n    title: 'Mechanical Keyboard',\n    price: '$89.99',\n    url: 'https://amazon.com/dp/B08KTZ8249?tag=yourtag-20', \n    post: '⌨️ RGB Mechanical Keyboard $89.99! Perfect for coding marathons 💻 https://amazon.com/dp/B08KTZ8249?tag=yourtag-20 #ad #dev'\n  }\n];\n\nconst pick = products[Math.floor(Math.random() * products.length)];\nreturn [{ json: pick }];"
      },
      "name": "Generate Post",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [400, 300],
      "id": "generate-002"
    }
  ],
  "connections": {
    "Daily Trigger": {
      "main": [[{"node": "Generate Post", "type": "main", "index": 0}]]
    }
  },
  "settings": {},
  "staticData": null,
  "meta": null,
  "pinData": {}
}
EOF

echo "📤 Deploying simple bot..."

# Deploy the workflow
RESULT=$(curl -s -X POST "$N8N_URL/api/v1/workflows" \
  -H "X-N8N-API-KEY: $API_KEY" \
  -H "Content-Type: application/json" \
  -d @/tmp/simple_bot.json)

# Check result
if echo "$RESULT" | jq -e '.id' > /dev/null 2>&1; then
    WORKFLOW_ID=$(echo "$RESULT" | jq -r '.id')
    IS_ACTIVE=$(echo "$RESULT" | jq -r '.active')
    
    echo "🎉 SUCCESS! Simple affiliate bot created!"
    echo "📋 Workflow ID: $WORKFLOW_ID"
    echo "🟢 Active: $IS_ACTIVE"
    echo ""
    
    if [ "$IS_ACTIVE" = "true" ]; then
        echo "✅ YOUR BOT IS LIVE AND RUNNING!"
        echo "📅 Will generate affiliate posts daily at 9 AM"
        echo "🤖 Using curated Amazon product data"
        echo ""
        echo "🌐 Access: $N8N_URL/workflow/$WORKFLOW_ID"
        echo "📊 Monitor: $N8N_URL/executions"
        
        # Test execution
        echo ""
        echo "🧪 Testing execution..."
        TEST_RESULT=$(curl -s -X POST "$N8N_URL/api/v1/workflows/$WORKFLOW_ID/execute" \
          -H "X-N8N-API-KEY: $API_KEY")
          
        if echo "$TEST_RESULT" | jq -e '.data' > /dev/null 2>&1; then
            echo "✅ Test successful! Check n8n UI for results"
        else
            echo "⚠️ Test may need verification - check UI"
        fi
        
        echo ""
        echo "🎯 THIS IS A WORKING FOUNDATION!"
        echo "💡 You can now add Claude, Bitly, X nodes to complete the automation"
        
    else
        echo "⚠️ Bot created but may need manual activation"
        echo "👉 Activate at: $N8N_URL/workflow/$WORKFLOW_ID"
    fi
    
else
    echo "❌ Failed to create workflow"
    echo "Response: $RESULT"
fi

# Cleanup
rm -f /tmp/simple_bot.json

echo ""
echo "✅ Simple bot deployment complete!"
