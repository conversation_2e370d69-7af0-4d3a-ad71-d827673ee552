#!/bin/bash

# 📊 AFFILIATE BOT MONITOR
# Check if your bot is active and monitor executions

N8N_URL="http://localhost:5678"
API_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ"
WORKFLOW_ID="y1o0TuUwO0NMSXFG"

echo "📊 Affiliate Bot Monitor"
echo "========================"

# Check workflow status
echo "🔍 Checking workflow status..."
STATUS=$(curl -s "$N8N_URL/api/v1/workflows/$WORKFLOW_ID" -H "X-N8N-API-KEY: $API_KEY" | jq -r '.active // "unknown"')

if [ "$STATUS" = "true" ]; then
    echo "🟢 BOT STATUS: LIVE! ✅"
    echo "📅 Next execution: Tomorrow at 9:00 AM"
    
    # Check recent executions
    echo ""
    echo "📊 Recent Executions:"
    curl -s "$N8N_URL/api/v1/executions?limit=5" -H "X-N8N-API-KEY: $API_KEY" | jq -r '.data[]? | "   \(.startedAt // "No date") - Status: \(.status // "unknown")"'
    
elif [ "$STATUS" = "false" ]; then
    echo "🟡 BOT STATUS: INACTIVE"
    echo "👉 Activate at: $N8N_URL/workflow/$WORKFLOW_ID"
    
else
    echo "❓ BOT STATUS: UNKNOWN"
    echo "👉 Check manually: $N8N_URL/workflow/$WORKFLOW_ID"
fi

echo ""
echo "🛠️  Quick Commands:"
echo "   • Monitor: ./monitor-bot.sh"
echo "   • View workflow: open $N8N_URL/workflow/$WORKFLOW_ID"
echo "   • View executions: open $N8N_URL/executions"

echo ""
echo "🎯 When LIVE, your bot will:"
echo "   • Post daily at 9 AM"
echo "   • Rotate categories (Electronics/Office/Home)"  
echo "   • Generate AI posts with Claude"
echo "   • Post to X with images"
echo "   • Log everything to Google Sheets"
