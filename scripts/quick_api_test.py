#!/usr/bin/env python3
"""
🚀 Quick n8n API Test
Direct test using your API key
"""

import requests
import json
from datetime import datetime

# Your API key
API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NjA3ODkyfQ.xRhDWfd3KL-jCWp51sDDIyBhs8TdXLUkAEJqhTSL0VI"

def test_n8n_api():
    """Test n8n API connection"""
    base_url = "http://localhost:5678/api/v1"
    headers = {
        "X-N8N-API-KEY": API_KEY,
        "Content-Type": "application/json"
    }
    
    print("🔌 Testing n8n API Connection...")
    print("=" * 50)
    
    # Test 1: Get workflows
    print("✅ Test 1: Getting workflows...")
    try:
        response = requests.get(f"{base_url}/workflows", headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! API response received")
            
            # Handle different response structures
            if isinstance(data, dict) and 'data' in data:
                workflows = data['data']
                print(f"   📊 Found {len(workflows)} workflows in data field")
                
                for workflow in workflows:
                    if isinstance(workflow, dict):
                        status = "🟢 ACTIVE" if workflow.get('active') else "🔴 INACTIVE"
                        print(f"   📋 {workflow.get('name', 'Unknown')} - {status}")
                        print(f"      ID: {workflow.get('id', 'Unknown')}")
                        print()
            elif isinstance(data, list):
                workflows = data
                print(f"   📊 Found {len(workflows)} workflows directly")
                
                for workflow in workflows:
                    if isinstance(workflow, dict):
                        status = "🟢 ACTIVE" if workflow.get('active') else "🔴 INACTIVE"
                        print(f"   📋 {workflow.get('name', 'Unknown')} - {status}")
                        print(f"      ID: {workflow.get('id', 'Unknown')}")
                        print()
            else:
                print(f"   📊 Response structure: {type(data)}")
                print(f"   📊 Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    # Test 2: Get executions
    print("✅ Test 2: Getting recent executions...")
    try:
        response = requests.get(f"{base_url}/executions?limit=5", headers=headers, timeout=10)
        if response.status_code == 200:
            executions = response.json()
            print(f"   ✅ Success! Found {len(executions.get('data', []))} recent executions")
            
            # Show execution details
            for execution in executions.get('data', [])[:3]:
                status = "✅ SUCCESS" if execution.get('finished') else "🔄 RUNNING"
                print(f"   📊 {execution['id'][:8]}... - {status}")
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Test workflow execution capability
    print("\n✅ Test 3: Testing workflow execution capability...")
    try:
        # Get the first active workflow
        response = requests.get(f"{base_url}/workflows", headers=headers, timeout=10)
        if response.status_code == 200:
            workflows = response.json()
            active_workflows = [w for w in workflows if w.get('active')]
            
            if active_workflows:
                test_workflow = active_workflows[0]
                print(f"   🎯 Found active workflow: {test_workflow['name']}")
                print(f"   🆔 ID: {test_workflow['id']}")
                print("   ℹ️  Ready to execute workflows via API!")
                
                # Test manual execution
                print("\n✅ Test 4: Testing manual workflow execution...")
                execute_response = requests.post(f"{base_url}/workflows/{test_workflow['id']}/execute", headers=headers, timeout=30)
                if execute_response.status_code == 200:
                    print("   🎉 Manual execution successful!")
                    execution_data = execute_response.json()
                    print(f"   📊 Execution ID: {execution_data.get('id', 'N/A')}")
                else:
                    print(f"   ⚠️  Manual execution failed: {execute_response.status_code}")
                    print(f"      Response: {execute_response.text}")
            else:
                print("   ⚠️  No active workflows found")
        else:
            print(f"   ❌ Failed to get workflows: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return True

def main():
    """Main function"""
    print("🚀 Quick n8n API Test")
    print("=" * 50)
    print(f"🕐 Test run at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔑 Using API key: {API_KEY[:20]}...{API_KEY[-20:]}")
    print()
    
    # Test the connection
    success = test_n8n_api()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 API Connection Test Complete!")
        print("✅ Your n8n API key is working correctly!")
        print("🚀 You're ready to control your affiliate bot programmatically!")
        print()
        print("🎯 Next Steps:")
        print("1. Update your affiliate bot scripts with this API key")
        print("2. Test the affiliate bot functionality")
        print("3. Monitor performance and go live!")
    else:
        print("❌ API Connection Test Failed!")
        print("🔧 Check your API key and n8n service status")

if __name__ == "__main__":
    main()
