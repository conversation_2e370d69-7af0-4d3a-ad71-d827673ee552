#!/usr/bin/env python3
"""
🎯 AFFILIATE AUTOMATION DASHBOARD
Shows the complete status of your 100% automated affiliate marketing system
"""

import os
import json
import subprocess
from datetime import datetime, timedelta
from pathlib import Path

class AutomationDashboard:
    def __init__(self):
        self.base_dir = Path("/Users/<USER>/n8n-self-hosted")
        self.log_file = self.base_dir / "affiliate_posts_log.json"
        self.bot_log = self.base_dir / "affiliate_bot.log"
        self.env_file = self.base_dir / ".env"
        
    def check_environment(self):
        """Check if environment is properly configured"""
        status = {
            "env_file": self.env_file.exists(),
            "x_credentials": False,
            "google_sheets": False,
            "virtual_env": (self.base_dir / "affiliate_bot_env").exists()
        }
        
        if status["env_file"]:
            with open(self.env_file, 'r') as f:
                content = f.read()
                status["x_credentials"] = "your_api_key_here" not in content
        
        status["google_sheets"] = (self.base_dir / "google_sheets_creds.json").exists()
        
        return status
    
    def get_last_run(self):
        """Get information about the last run"""
        if not self.log_file.exists():
            return None
        
        try:
            with open(self.log_file, 'r') as f:
                logs = json.load(f)
            
            if logs:
                last = logs[-1]
                timestamp = datetime.strptime(last['timestamp'], "%Y-%m-%d %H:%M:%S")
                hours_ago = (datetime.now() - timestamp).total_seconds() / 3600
                return {
                    "timestamp": last['timestamp'],
                    "product": last['title'],
                    "price": last['price'],
                    "commission": last['commission'],
                    "posted": last['posted'] == 'TRUE',
                    "hours_ago": hours_ago
                }
        except:
            pass
        
        return None
    
    def check_cron_job(self):
        """Check if cron job is set up"""
        try:
            result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
            return "affiliate_bot" in result.stdout
        except:
            return False
    
    def get_n8n_workflows(self):
        """Get n8n workflow status"""
        try:
            api_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ"
            
            import requests
            headers = {"X-N8N-API-KEY": api_key}
            response = requests.get("http://localhost:5678/api/v1/workflows", headers=headers)
            
            if response.status_code == 200:
                workflows = response.json()['data']
                return [{"name": w['name'], "active": w['active']} for w in workflows]
        except:
            pass
        
        return []
    
    def get_total_earnings(self):
        """Calculate total potential earnings"""
        if not self.log_file.exists():
            return 0
        
        try:
            with open(self.log_file, 'r') as f:
                logs = json.load(f)
            
            total = sum(float(entry.get('commission', 0)) for entry in logs)
            return total
        except:
            return 0
    
    def display_dashboard(self):
        """Display the complete dashboard"""
        print("\n" + "="*70)
        print("                 🎯 AFFILIATE AUTOMATION DASHBOARD")
        print("="*70)
        
        # Environment Status
        env_status = self.check_environment()
        print("\n📋 ENVIRONMENT STATUS:")
        print(f"  • Virtual Environment: {'✅ Ready' if env_status['virtual_env'] else '❌ Missing'}")
        print(f"  • .env File: {'✅ Exists' if env_status['env_file'] else '❌ Missing'}")
        print(f"  • X.com Credentials: {'✅ Configured' if env_status['x_credentials'] else '⚠️ Not Configured'}")
        print(f"  • Google Sheets: {'✅ Ready' if env_status['google_sheets'] else '⚠️ Not Configured'}")
        
        # Automation Status
        print("\n🤖 AUTOMATION STATUS:")
        cron_active = self.check_cron_job()
        print(f"  • Cron Job (9 AM Daily): {'✅ Active' if cron_active else '❌ Not Set'}")
        
        workflows = self.get_n8n_workflows()
        if workflows:
            print("  • n8n Workflows:")
            for w in workflows:
                status = "✅ Active" if w['active'] else "❌ Inactive"
                print(f"    - {w['name']}: {status}")
        else:
            print("  • n8n Workflows: ⚠️ Could not check")
        
        # Last Run Info
        last_run = self.get_last_run()
        print("\n📊 LAST RUN:")
        if last_run:
            print(f"  • Time: {last_run['timestamp']} ({last_run['hours_ago']:.1f} hours ago)")
            print(f"  • Product: {last_run['product']}")
            print(f"  • Price: {last_run['price']}")
            print(f"  • Commission: ${last_run['commission']}")
            print(f"  • Posted to X: {'✅ Yes' if last_run['posted'] else '❌ No'}")
            
            if last_run['hours_ago'] > 25:
                print("  ⚠️ WARNING: Last run was over 24 hours ago!")
        else:
            print("  ⚠️ No runs recorded yet")
        
        # Earnings Summary
        total_earnings = self.get_total_earnings()
        print("\n💰 EARNINGS POTENTIAL:")
        print(f"  • Total Potential Commissions: ${total_earnings:.2f}")
        if self.log_file.exists():
            with open(self.log_file, 'r') as f:
                posts = len(json.load(f))
            print(f"  • Total Posts Created: {posts}")
            if posts > 0:
                print(f"  • Average per Post: ${total_earnings/posts:.2f}")
        
        # Quick Actions
        print("\n⚡ QUICK ACTIONS:")
        print("  1. Run Now: ./run_affiliate_bot.sh")
        print("  2. Check Logs: tail -20 affiliate_bot.log")
        print("  3. Edit Credentials: nano .env")
        print("  4. View Today's Post: cat today-affiliate-opportunity.txt")
        print("  5. Monitor Status: python3 monitor_affiliate_bot.py")
        
        # Next Scheduled Run
        if cron_active:
            now = datetime.now()
            next_9am = now.replace(hour=9, minute=0, second=0, microsecond=0)
            if now.hour >= 9:
                next_9am += timedelta(days=1)
            hours_until = (next_9am - now).total_seconds() / 3600
            print(f"\n⏰ NEXT SCHEDULED RUN: {next_9am.strftime('%Y-%m-%d %H:%M')} ({hours_until:.1f} hours)")
        
        print("\n" + "="*70)
        
        # Status Summary
        if env_status['x_credentials'] and cron_active:
            print("✅ SYSTEM STATUS: FULLY AUTOMATED - Making money while you sleep!")
        elif cron_active:
            print("⚠️ SYSTEM STATUS: PARTIALLY AUTOMATED - Add X.com credentials to post automatically")
        else:
            print("❌ SYSTEM STATUS: MANUAL MODE - Run setup_full_automation.sh to enable automation")
        
        print("="*70 + "\n")

def main():
    dashboard = AutomationDashboard()
    dashboard.display_dashboard()
    
    print("OPTIONS:")
    print("  [R] Run affiliate bot now")
    print("  [L] View latest logs")
    print("  [T] Show today's affiliate post")
    print("  [Q] Quit")
    print()
    
    choice = input("Select option (or Q to quit): ").strip().upper()
    
    if choice == 'R':
        print("\n🚀 Running affiliate bot...")
        os.system("/Users/<USER>/n8n-self-hosted/run_affiliate_bot.sh")
    elif choice == 'L':
        print("\n📋 Latest log entries:")
        os.system("tail -20 /Users/<USER>/n8n-self-hosted/affiliate_bot.log")
    elif choice == 'T':
        print("\n📝 Today's affiliate post:")
        os.system("cat /Users/<USER>/n8n-self-hosted/today-affiliate-opportunity.txt")
    
    print("\n👋 Dashboard closed. Run again anytime with: python3 automation_dashboard.py")

if __name__ == "__main__":
    main()
