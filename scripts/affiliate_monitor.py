#!/usr/bin/env python3
"""
📊 AFFILIATE BOT MONITORING DASHBOARD 📊
Real-time monitoring and management of your affiliate automation
"""

import requests
import json
import time
from datetime import datetime, timedelta
import os
import sys

N8N_URL = "http://localhost:5678"
API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ"

class AffiliateMonitor:
    def __init__(self):
        self.headers = {
            "X-N8N-API-KEY": API_KEY,
            "Content-Type": "application/json"
        }
        self.workflow_id = self.get_workflow_id()
    
    def get_workflow_id(self):
        """Get the affiliate workflow ID"""
        if os.path.exists('.affiliate_workflow_id'):
            with open('.affiliate_workflow_id', 'r') as f:
                return f.read().strip()
        return None
    
    def log(self, message, level="INFO"):
        """Enhanced logging"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        icons = {"INFO": "ℹ️", "SUCCESS": "✅", "ERROR": "❌", "WARN": "⚠️"}
        print(f"{icons.get(level, 'ℹ️')} [{timestamp}] {message}")
    
    def get_workflow_status(self):
        """Get current workflow status"""
        if not self.workflow_id:
            return None
        
        try:
            response = requests.get(
                f"{N8N_URL}/api/v1/workflows/{self.workflow_id}",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            self.log(f"Failed to get workflow status: {e}", "ERROR")
            return None
    
    def get_executions(self, limit=10):
        """Get recent workflow executions"""
        try:
            response = requests.get(
                f"{N8N_URL}/api/v1/executions?limit={limit}",
                headers=self.headers
            )
            if response.status_code == 200:
                executions = response.json().get('data', [])
                # Filter for our workflow
                if self.workflow_id:
                    executions = [e for e in executions if e.get('workflowId') == self.workflow_id]
                return executions
            return []
        except Exception as e:
            self.log(f"Failed to get executions: {e}", "ERROR")
            return []
    
    def get_execution_details(self, execution_id):
        """Get detailed execution information"""
        try:
            response = requests.get(
                f"{N8N_URL}/api/v1/executions/{execution_id}",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            self.log(f"Failed to get execution details: {e}", "ERROR")
            return None
    
    def show_dashboard(self):
        """Display comprehensive monitoring dashboard"""
        print("\\n" + "="*60)
        print("📊 AFFILIATE BOT MONITORING DASHBOARD")
        print("="*60)
        
        # Workflow Status
        workflow = self.get_workflow_status()
        if workflow:
            active = workflow.get('active', False)
            last_updated = workflow.get('updatedAt', '')[:19].replace('T', ' ')
            
            print(f"\\n🤖 WORKFLOW STATUS:")
            print(f"   📋 Name: {workflow.get('name', 'Unknown')}")
            print(f"   🆔 ID: {self.workflow_id}")
            print(f"   {'🟢 ACTIVE' if active else '🔴 INACTIVE'}")
            print(f"   📅 Last Updated: {last_updated}")
        else:
            print("\\n❌ Workflow not found or not accessible")
            return
        
        # Recent Executions
        executions = self.get_executions(10)
        if executions:
            print(f"\\n📈 RECENT EXECUTIONS ({len(executions)} found):")
            print("   " + "-"*50)
            
            success_count = sum(1 for e in executions if e.get('status') == 'success')
            error_count = sum(1 for e in executions if e.get('status') == 'error')
            running_count = sum(1 for e in executions if e.get('status') == 'running')
            
            print(f"   ✅ Success: {success_count} | ❌ Failed: {error_count} | 🔄 Running: {running_count}")
            print("   " + "-"*50)
            
            for i, execution in enumerate(executions[:5]):  # Show last 5
                status = execution.get('status', 'unknown')
                started = execution.get('startedAt', '')[:19].replace('T', ' ')
                duration = self.calculate_duration(execution)
                
                status_icon = {
                    'success': '✅',
                    'error': '❌',
                    'running': '🔄',
                    'waiting': '⏳'
                }.get(status, '❓')
                
                print(f"   {status_icon} {started} - {status.upper()} ({duration})")
        else:
            print("\\n📈 No recent executions found")
        
        # Performance Summary
        self.show_performance_summary(executions)
        
        # Next Execution Prediction
        self.show_next_execution_time()
        
    def calculate_duration(self, execution):
        """Calculate execution duration"""
        started = execution.get('startedAt')
        finished = execution.get('finishedAt')
        
        if started and finished:
            try:
                start_time = datetime.fromisoformat(started.replace('Z', '+00:00'))
                end_time = datetime.fromisoformat(finished.replace('Z', '+00:00'))
                duration = end_time - start_time
                return f"{duration.total_seconds():.1f}s"
            except:
                return "unknown"
        elif started:
            try:
                start_time = datetime.fromisoformat(started.replace('Z', '+00:00'))
                duration = datetime.now() - start_time.replace(tzinfo=None)
                return f"running {duration.total_seconds():.0f}s"
            except:
                return "running"
        return "unknown"
    
    def show_performance_summary(self, executions):
        """Show performance analytics"""
        if not executions:
            return
        
        print("\\n📊 PERFORMANCE ANALYTICS:")
        
        # Success rate
        success_count = sum(1 for e in executions if e.get('status') == 'success')
        total_count = len(executions)
        success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
        
        print(f"   🎯 Success Rate: {success_rate:.1f}% ({success_count}/{total_count})")
        
        # Average duration for completed executions
        completed_executions = [e for e in executions if e.get('finishedAt')]
        if completed_executions:
            durations = []
            for execution in completed_executions:
                started = execution.get('startedAt')
                finished = execution.get('finishedAt')
                if started and finished:
                    try:
                        start_time = datetime.fromisoformat(started.replace('Z', '+00:00'))
                        end_time = datetime.fromisoformat(finished.replace('Z', '+00:00'))
                        duration = end_time - start_time
                        durations.append(duration.total_seconds())
                    except:
                        continue
            
            if durations:
                avg_duration = sum(durations) / len(durations)
                print(f"   ⏱️  Average Duration: {avg_duration:.1f}s")
        
        # Recent trend
        recent_executions = executions[:5]  # Last 5 executions
        recent_success = sum(1 for e in recent_executions if e.get('status') == 'success')
        if recent_executions:
            recent_rate = (recent_success / len(recent_executions)) * 100
            trend = "📈 Improving" if recent_rate > success_rate else "📉 Declining" if recent_rate < success_rate else "➡️ Stable"
            print(f"   📈 Recent Trend: {trend} ({recent_rate:.0f}% last 5 runs)")
    
    def show_next_execution_time(self):
        """Predict next execution time"""
        print("\\n⏰ SCHEDULE INFO:")
        print("   📅 Daily execution at 9:00 AM")
        
        now = datetime.now()
        today_9am = now.replace(hour=9, minute=0, second=0, microsecond=0)
        tomorrow_9am = today_9am + timedelta(days=1)
        
        if now < today_9am:
            next_run = today_9am
            print(f"   ⏰ Next run: Today at 9:00 AM ({(next_run - now).total_seconds()/3600:.1f} hours)")
        else:
            next_run = tomorrow_9am
            print(f"   ⏰ Next run: Tomorrow at 9:00 AM ({(next_run - now).total_seconds()/3600:.1f} hours)")
    
    def show_detailed_execution(self, execution_id):
        """Show detailed execution information"""
        execution = self.get_execution_details(execution_id)
        if not execution:
            self.log(f"Execution {execution_id} not found", "ERROR")
            return
        
        print(f"\\n📋 EXECUTION DETAILS: {execution_id}")
        print("="*50)
        
        print(f"Status: {execution.get('status', 'unknown').upper()}")
        print(f"Started: {execution.get('startedAt', 'unknown')}")
        print(f"Finished: {execution.get('finishedAt', 'not finished')}")
        print(f"Duration: {self.calculate_duration(execution)}")
        
        # Show node execution data if available
        if 'data' in execution and 'resultData' in execution['data']:
            result_data = execution['data']['resultData']
            print(f"\\n🔧 NODE EXECUTION RESULTS:")
            
            for node_name, node_data in result_data.get('runData', {}).items():
                if node_data and len(node_data) > 0:
                    latest_run = node_data[-1]  # Get latest run of the node
                    if 'data' in latest_run:
                        data_count = len(latest_run['data'].get('main', [[]])[0])
                        print(f"   📦 {node_name}: {data_count} items processed")
        
        # Show error details if failed
        if execution.get('status') == 'error' and 'data' in execution:
            error_data = execution['data'].get('resultData', {})
            if 'error' in error_data:
                error = error_data['error']
                print(f"\\n❌ ERROR DETAILS:")
                print(f"   Message: {error.get('message', 'Unknown error')}")
                if 'node' in error:
                    print(f"   Failed Node: {error['node'].get('name', 'Unknown')}")
    
    def live_monitor(self):
        """Live monitoring mode"""
        print("🔴 LIVE MONITORING MODE (Press Ctrl+C to exit)")
        print("="*50)
        
        try:
            while True:
                self.show_dashboard()
                print(f"\\n🔄 Refreshing in 30 seconds...")
                time.sleep(30)
                print("\\033[H\\033[J")  # Clear screen
        except KeyboardInterrupt:
            print("\\n\\n👋 Live monitoring stopped")

def main():
    monitor = AffiliateMonitor()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "--live":
            monitor.live_monitor()
        elif command == "--execution" and len(sys.argv) > 2:
            execution_id = sys.argv[2]
            monitor.show_detailed_execution(execution_id)
        elif command == "--help":
            print("""
📊 AFFILIATE BOT MONITOR COMMANDS:
  
  python affiliate_monitor.py           - Show dashboard
  python affiliate_monitor.py --live    - Live monitoring
  python affiliate_monitor.py --execution <id> - Detailed execution view
  python affiliate_monitor.py --help    - Show this help
            """)
        else:
            print("Unknown command. Use --help for usage.")
    else:
        monitor.show_dashboard()

if __name__ == "__main__":
    main()
