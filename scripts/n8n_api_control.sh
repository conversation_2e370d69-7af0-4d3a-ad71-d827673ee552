#!/bin/bash

# n8n API Control Script
# Set your API key and base URL
N8N_API_KEY="${N8N_API_KEY:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ}"
N8N_BASE_URL="http://localhost:5678"
API_URL="${N8N_BASE_URL}/api/v1"

# Common headers
HEADERS=(-H "X-N8N-API-KEY: ${N8N_API_KEY}" -H "Content-Type: application/json")

# Functions for common operations

# Get all workflows
get_workflows() {
    echo "📋 Getting all workflows..."
    curl -s "${HEADERS[@]}" "${API_URL}/workflows" | jq '.'
}

# Get specific workflow by ID
get_workflow() {
    local workflow_id=$1
    if [[ -z "$workflow_id" ]]; then
        echo "Usage: get_workflow <workflow_id>"
        return 1
    fi
    echo "📋 Getting workflow ${workflow_id}..."
    curl -s "${HEADERS[@]}" "${API_URL}/workflows/${workflow_id}" | jq '.'
}

# Execute workflow
execute_workflow() {
    local workflow_id=$1
    if [[ -z "$workflow_id" ]]; then
        echo "Usage: execute_workflow <workflow_id>"
        return 1
    fi
    echo "🚀 Executing workflow ${workflow_id}..."
    curl -s -X POST "${HEADERS[@]}" "${API_URL}/workflows/${workflow_id}/execute" | jq '.'
}

# Get executions
get_executions() {
    echo "📊 Getting executions..."
    curl -s "${HEADERS[@]}" "${API_URL}/executions" | jq '.'
}

# Get execution by ID
get_execution() {
    local execution_id=$1
    if [[ -z "$execution_id" ]]; then
        echo "Usage: get_execution <execution_id>"
        return 1
    fi
    echo "📊 Getting execution ${execution_id}..."
    curl -s "${HEADERS[@]}" "${API_URL}/executions/${execution_id}" | jq '.'
}

# Create workflow
create_workflow() {
    local workflow_file=$1
    if [[ -z "$workflow_file" || ! -f "$workflow_file" ]]; then
        echo "Usage: create_workflow <workflow.json>"
        return 1
    fi
    echo "📝 Creating workflow from ${workflow_file}..."
    curl -s -X POST "${HEADERS[@]}" -d "@${workflow_file}" "${API_URL}/workflows" | jq '.'
}

# Update workflow
update_workflow() {
    local workflow_id=$1
    local workflow_file=$2
    if [[ -z "$workflow_id" || -z "$workflow_file" || ! -f "$workflow_file" ]]; then
        echo "Usage: update_workflow <workflow_id> <workflow.json>"
        return 1
    fi
    echo "📝 Updating workflow ${workflow_id}..."
    curl -s -X PUT "${HEADERS[@]}" -d "@${workflow_file}" "${API_URL}/workflows/${workflow_id}" | jq '.'
}

# Delete workflow
delete_workflow() {
    local workflow_id=$1
    if [[ -z "$workflow_id" ]]; then
        echo "Usage: delete_workflow <workflow_id>"
        return 1
    fi
    echo "🗑️  Deleting workflow ${workflow_id}..."
    curl -s -X DELETE "${HEADERS[@]}" "${API_URL}/workflows/${workflow_id}"
}

# Activate/Deactivate workflow
toggle_workflow() {
    local workflow_id=$1
    local active=$2
    if [[ -z "$workflow_id" || -z "$active" ]]; then
        echo "Usage: toggle_workflow <workflow_id> <true|false>"
        return 1
    fi
    echo "⚡ Setting workflow ${workflow_id} active=${active}..."
    curl -s -X PATCH "${HEADERS[@]}" -d "{\"active\": ${active}}" "${API_URL}/workflows/${workflow_id}" | jq '.'
}

# Get workflow status/info
get_workflow_status() {
    echo "ℹ️  n8n Instance Info..."
    curl -s "${HEADERS[@]}" "${API_URL}/workflows" | jq '.data | length as $count | {workflow_count: $count}'
    curl -s "${HEADERS[@]}" "${API_URL}/executions" | jq '.data | length as $count | {execution_count: $count}'
}

# Help function
show_help() {
    echo "n8n API Control Script"
    echo "====================="
    echo ""
    echo "Available functions:"
    echo "  get_workflows                    - List all workflows"
    echo "  get_workflow <id>               - Get specific workflow"
    echo "  create_workflow <file.json>     - Create workflow from JSON file"
    echo "  update_workflow <id> <file.json> - Update workflow"
    echo "  delete_workflow <id>            - Delete workflow"
    echo "  execute_workflow <id>           - Execute workflow"
    echo "  toggle_workflow <id> <true|false> - Activate/deactivate workflow"
    echo "  get_executions                  - List all executions"
    echo "  get_execution <id>              - Get specific execution"
    echo "  get_workflow_status             - Show instance status"
    echo ""
    echo "Examples:"
    echo "  source n8n_api_control.sh"
    echo "  get_workflows"
    echo "  execute_workflow 123"
    echo "  toggle_workflow 123 true"
}

# If script is run directly (not sourced), show help
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    show_help
fi
