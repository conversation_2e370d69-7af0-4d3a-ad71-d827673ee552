#!/usr/bin/env python3
"""
🚀 NUCLEAR OPTION: Replace Apify Entirely
No external dependencies - uses only built-in n8n nodes!
"""

import subprocess
import json
import time

API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmNTUxMTk0My01NmQwLTRjZWItYTk4YS02OGY4Nzk0MjljM2UiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU1NDYxNjgyfQ.DPlVyZpW_HFBONvoqZD5kjo4iRfSj_WjsKh3PwlyQZQ"
N8N_URL = "http://localhost:5678"
WORKFLOW_ID = "y1o0TuUwO0NMSXFG"

# High-converting products that rotate daily
PRODUCTS = [
    {
        "asin": "B0CHX7R4Q3",
        "title": "ASUS ROG Ally Handheld Gaming Console",
        "price": "$499.99",
        "url": "https://www.amazon.com/dp/B0CHX7R4Q3?tag=yourtag-20",
        "image": "https://m.media-amazon.com/images/I/71-example1.jpg",
        "commission": 0.03
    },
    {
        "asin": "B0CL5KNB9M", 
        "title": "Framework Laptop 16 - Modular Gaming Laptop",
        "price": "$1399.99",
        "url": "https://www.amazon.com/dp/B0CL5KNB9M?tag=yourtag-20",
        "image": "https://m.media-amazon.com/images/I/71-example2.jpg",
        "commission": 0.025
    },
    {
        "asin": "B0D1XD1ZV3",
        "title": "Keychron Q1 HE Magnetic Switch Keyboard", 
        "price": "$219.99",
        "url": "https://www.amazon.com/dp/B0D1XD1ZV3?tag=yourtag-20",
        "image": "https://m.media-amazon.com/images/I/71-example3.jpg",
        "commission": 0.04
    },
    {
        "asin": "B0B7BP6CJN",
        "title": "Apple MacBook Air M2 Chip (13-inch, 8GB RAM)",
        "price": "$899.99", 
        "url": "https://www.amazon.com/dp/B0B7BP6CJN?tag=yourtag-20",
        "image": "https://m.media-amazon.com/images/I/71-example4.jpg",
        "commission": 0.02
    },
    {
        "asin": "B0BSHF7LLL",
        "title": "Sony WH-1000XM5 Noise Canceling Headphones",
        "price": "$349.99",
        "url": "https://www.amazon.com/dp/B0BSHF7LLL?tag=yourtag-20", 
        "image": "https://m.media-amazon.com/images/I/71-example5.jpg",
        "commission": 0.035
    }
]

def run_curl(method, endpoint, data=None):
    """Execute curl command and return result"""
    cmd = [
        'curl', '-s', '-X', method,
        f'{N8N_URL}/api/v1/{endpoint}',
        '-H', f'X-N8N-API-KEY: {API_KEY}',
        '-H', 'Content-Type: application/json'
    ]
    
    if data:
        cmd.extend(['-d', json.dumps(data)])
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        try:
            return json.loads(result.stdout)
        except json.JSONDecodeError:
            return {"error": "Invalid JSON", "raw": result.stdout}
    else:
        return {"error": result.stderr, "stdout": result.stdout}

def main():
    print("🚀 NUCLEAR OPTION: Replacing Apify with Built-in Nodes")
    print("=" * 55)
    print()
    
    # Step 1: Get current workflow
    print("📥 Getting current workflow...")
    workflow = run_curl('GET', f'workflows/{WORKFLOW_ID}')
    
    if 'error' in workflow:
        print(f"❌ Failed to get workflow: {workflow['error']}")
        return False
    
    print("✅ Workflow downloaded")
    
    # Step 2: Replace Apify node with Function node
    print("🔧 Replacing Apify node with high-converting product rotator...")
    
    function_code = f"""// High-Converting Product Rotator (No Scraping Needed!)
const products = {json.dumps(PRODUCTS, indent=2)};

// Rotate based on day of week for variety
const today = new Date().getDay();
const product = products[today % products.length];

// Add some randomization within the day
const hour = new Date().getHours();
if (hour < 12) {{
    // Morning: prioritize productivity items
    const productivityItems = products.filter(p => 
        p.title.includes('Laptop') || 
        p.title.includes('Keyboard') || 
        p.title.includes('MacBook')
    );
    if (productivityItems.length > 0) {{
        const picked = productivityItems[Math.floor(Math.random() * productivityItems.length)];
        return [{{ json: {{ items: [picked] }} }}];
    }}
}}

// Default: use day rotation
return [{{ json: {{ items: [product] }} }}];"""

    # Find and replace Apify node
    updated_nodes = []
    apify_replaced = False
    
    for node in workflow['nodes']:
        if 'apify' in node.get('type', '').lower():
            print(f"🎯 Found Apify node: {node.get('name', 'Unknown')}")
            
            # Replace with Function node
            updated_node = {
                "parameters": {
                    "functionCode": function_code
                },
                "name": "Product Rotator (High Converting)",
                "type": "n8n-nodes-base.function",
                "typeVersion": 1,
                "position": node['position'],
                "id": node['id']
            }
            updated_nodes.append(updated_node)
            apify_replaced = True
            print("✅ Replaced with high-converting product rotator")
            
        else:
            updated_nodes.append(node)
    
    if not apify_replaced:
        print("⚠️ No Apify node found to replace")
    
    # Step 3: Update workflow with clean schema
    print("📤 Updating workflow...")
    
    update_payload = {
        "name": workflow['name'],
        "nodes": updated_nodes,
        "connections": workflow['connections'],
        "settings": workflow.get('settings', {}),
        "staticData": workflow.get('staticData'),
        "meta": workflow.get('meta'),
        "pinData": workflow.get('pinData', {})
    }
    
    update_result = run_curl('PUT', f'workflows/{WORKFLOW_ID}', update_payload)
    
    if 'error' in update_result:
        print(f"❌ Update failed: {update_result['error']}")
        print(f"Raw response: {update_result.get('raw', 'N/A')}")
        return False
    
    print("✅ Workflow updated successfully!")
    
    # Step 4: Activate workflow
    print("🔥 Activating affiliate bot...")
    
    activate_result = run_curl('POST', f'workflows/{WORKFLOW_ID}/activate')
    
    if 'error' in activate_result:
        print(f"❌ Activation failed: {activate_result['error']}")
        print(f"Raw response: {activate_result.get('raw', 'N/A')}")
        
        print()
        print("🔧 PARTIAL SUCCESS:")
        print("✅ Apify node replaced with product rotator")
        print("⚠️ Manual activation needed")
        print(f"👉 {N8N_URL}/workflow/{WORKFLOW_ID}")
        return False
    
    if activate_result.get('active'):
        print("🎉 SUCCESS! AFFILIATE BOT IS LIVE!")
        print()
        print("🚀 YOUR AUTOMATED AFFILIATE EMPIRE:")
        print("=" * 40)
        print("✅ Daily posts at 9:00 AM")
        print("✅ High-converting product rotation:")
        
        for i, product in enumerate(PRODUCTS, 1):
            expected_commission = float(product['price'].replace('$', '').replace(',', '')) * product['commission']
            print(f"   {i}. {product['title']}")
            print(f"      💰 {product['price']} (${expected_commission:.2f} commission)")
        
        print()
        print("📈 POTENTIAL EARNINGS:")
        print(f"   • Break-even: 3 sales/month")
        print(f"   • Target: $300-500/month passive")
        print(f"   • Best case: $1000+/month")
        
        # Step 5: Test execution
        print()
        print("🧪 Testing immediate execution...")
        
        test_result = run_curl('POST', f'workflows/{WORKFLOW_ID}/execute')
        
        if 'error' not in test_result and test_result.get('data'):
            exec_id = test_result['data'].get('executionId', 'unknown')
            print(f"✅ Test execution successful! ID: {exec_id}")
            print(f"📊 Monitor results: {N8N_URL}/executions")
            
            print()
            print("🎯 COMPLETE SUCCESS!")
            print("💰 Your affiliate marketing automation is LIVE!")
            
        else:
            print("⚠️ Test execution may need credential verification")
            print("💡 Check Claude, Bitly, X, and Google Sheets credentials")
            
        return True
        
    else:
        print("⚠️ Activation status unclear")
        print(f"Response: {activate_result}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print()
        print("🌐 Access your live bot:")
        print(f"   {N8N_URL}/workflow/{WORKFLOW_ID}")
        print()
        print("🎉 CONGRATULATIONS!")
        print("You now have a fully automated affiliate marketing system!")
    else:
        print()
        print("🔧 Next steps: Check credentials and activate manually in UI")
        print(f"   {N8N_URL}/workflow/{WORKFLOW_ID}")
