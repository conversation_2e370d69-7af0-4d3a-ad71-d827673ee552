#!/usr/bin/env python3
"""
🎯 PLAYWRIGHT X.COM AUTOMATION - MOST RELIABLE
Superior to Selenium - harder to detect, more stable
"""

import os
import sys
import json
import random
import asyncio
from pathlib import Path
from datetime import datetime

# Check if playwright is installed
try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

def install_playwright():
    """Install Playwright and browsers"""
    print("📦 Installing Playwright...")
    os.system("pip3 install playwright --quiet")
    print("🌐 Installing browsers...")
    os.system("playwright install chromium")
    print("✅ Playwright setup complete!")

def get_daily_product():
    """Get today's featured product"""
    PRODUCTS = [
        {
            "asin": "B0BSHF7LLL",
            "title": "Apple AirPods (3rd Gen)",
            "price": "$169.99",
            "commission": 6.80,
            "hashtags": "#Apple #AirPods #tech #deals"
        },
        {
            "asin": "B08C1W5N87", 
            "title": "Fire TV Stick 4K",
            "price": "$49.99",
            "commission": 2.00,
            "hashtags": "#FireTV #streaming #4K"
        },
        {
            "asin": "B0B3PSRHHN",
            "title": "Bose QuietComfort 45",
            "price": "$279.00",
            "commission": 11.16,
            "hashtags": "#Bose #headphones #audio"
        },
        {
            "asin": "B08H75RTZ8",
            "title": "iPad 9th Gen",
            "price": "$329.99",
            "commission": 13.20,
            "hashtags": "#iPad #Apple #tablet"
        },
        {
            "asin": "B0BL45J5CK",
            "title": "Sony WH-1000XM5",
            "price": "$398.00",
            "commission": 15.92,
            "hashtags": "#Sony #headphones #premium"
        }
    ]
    
    day = datetime.now().timetuple().tm_yday
    return PRODUCTS[day % len(PRODUCTS)]

def create_post_content(product):
    """Create engaging affiliate post"""
    url = f"https://www.amazon.com/dp/{product['asin']}?tag=igorganapolsk-20"
    
    templates = [
        f"🔥 HOT DEAL: {product['title']} for {product['price']}!\n\nDon't miss this amazing price!\n\n👉 {url}\n\n{product['hashtags']} #ad",
        f"💰 {product['title']} - Only {product['price']}\n\nBest price I've seen!\n\n🛒 {url}\n\n{product['hashtags']} #deals #ad",
        f"⚡ {product['title']} at {product['price']}\n\nLimited time offer!\n\n✅ {url}\n\n{product['hashtags']} #sale #ad"
    ]
    
    return random.choice(templates)

async def post_with_playwright(username, password, content, headless=False):
    """Post to X.com using Playwright"""
    
    print("\n🎭 STARTING PLAYWRIGHT AUTOMATION...")
    
    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(
            headless=headless,
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-features=IsolateOrigins,site-per-process'
            ]
        )
        
        # Create context with anti-detection measures
        context = await browser.new_context(
            viewport={'width': 1280, 'height': 720},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        )
        
        # Create page
        page = await context.new_page()
        
        try:
            print("📱 Opening X.com...")
            await page.goto('https://x.com/login')
            
            # Login
            print("📝 Entering username...")
            await page.fill('input[name="text"]', username)
            await page.press('input[name="text"]', 'Enter')
            
            await page.wait_for_timeout(2000)
            
            # Password
            print("🔐 Entering password...")
            await page.fill('input[name="password"]', password)
            await page.press('input[name="password"]', 'Enter')
            
            print("⏳ Logging in...")
            await page.wait_for_timeout(5000)
            
            # Compose tweet
            print("✍️ Composing post...")
            await page.click('[data-testid="SideNav_NewTweet_Button"]')
            
            await page.wait_for_timeout(2000)
            
            # Type tweet
            await page.fill('[data-testid="tweetTextarea_0"]', content)
            
            await page.wait_for_timeout(2000)
            
            # Post it
            print("📤 Posting...")
            await page.click('[data-testid="tweetButtonInline"]')
            
            print("✅ SUCCESSFULLY POSTED TO X.COM!")
            
            await page.wait_for_timeout(3000)
            
            # Take screenshot as proof
            await page.screenshot(path='post_success.png')
            print("📸 Screenshot saved: post_success.png")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
            
        finally:
            await browser.close()

def create_automated_script():
    """Create the final automation script"""
    
    script = '''#!/usr/bin/env python3
"""
FULLY AUTOMATED X.COM AFFILIATE POSTER
"""
import asyncio
import os
from x_playwright_automation import get_daily_product, create_post_content, post_with_playwright

async def main():
    # Credentials
    USERNAME = "IgorGanapolsky"
    PASSWORD = os.getenv("X_PASSWORD", "")  # Set in environment
    
    if not PASSWORD:
        print("⚠️ Set X_PASSWORD environment variable!")
        return
    
    # Get product and create post
    product = get_daily_product()
    content = create_post_content(product)
    
    print(f"Product: {product['title']}")
    print(f"Commission: ${product['commission']}")
    print(f"Post: {content}")
    
    # Post it
    success = await post_with_playwright(USERNAME, PASSWORD, content, headless=True)
    
    if success:
        print("💰 Money making post published!")

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    with open('auto_post_x.py', 'w') as f:
        f.write(script)
    
    os.chmod('auto_post_x.py', 0o755)
    print("✅ Script created: auto_post_x.py")

def main():
    print("="*70)
    print("      🎭 PLAYWRIGHT X.COM AUTOMATION")
    print("="*70)
    
    if not PLAYWRIGHT_AVAILABLE:
        install_playwright()
    
    print("\n📊 THE FULL AUTOMATION SOLUTION:")
    print("""
    ✅ WHY THIS WORKS:
    • Bypasses ALL API limitations
    • 100% FREE (no $100/month API fee)
    • Mimics real browser behavior
    • Harder to detect than Selenium
    • Works with any X.com account
    
    ✅ WHAT IT DOES:
    • Logs into X.com automatically
    • Posts your affiliate links daily
    • Rotates through products
    • Takes screenshots as proof
    • Runs headless (background)
    
    ✅ SETUP:
    1. Set your password:
       export X_PASSWORD="your_password"
    
    2. Run once to test:
       python3 auto_post_x.py
    
    3. Add to cron for daily posting:
       0 9 * * * X_PASSWORD="your_password" /usr/bin/python3 /path/to/auto_post_x.py
    """)
    
    create_automated_script()
    
    print("\n🎯 YOUR AFFILIATE BOT IS READY!")
    print("Just set your password and run!")
    
    # Show example
    product = get_daily_product()
    content = create_post_content(product)
    print(f"\n📝 Today's post preview:")
    print("-"*50)
    print(content)
    print("-"*50)
    print(f"💰 Commission: ${product['commission']} per sale")

if __name__ == "__main__":
    main()
